{"name": "core.js", "main": "client/core.js", "version": "2.6.11", "description": "Standard Library", "keywords": ["ES3", "ES5", "ES6", "ES7", "ES2015", "ES2016", "ES2017", "ECMAScript 3", "ECMAScript 5", "ECMAScript 6", "ECMAScript 7", "ECMAScript 2015", "ECMAScript 2016", "ECMAScript 2017", "Harmony", "<PERSON><PERSON><PERSON>", "Map", "Set", "WeakMap", "WeakSet", "Promise", "Symbol", "TypedArray", "setImmediate", "Dict", "polyfill", "shim"], "authors": ["<PERSON> <<EMAIL>> (http://zloirock.ru/)"], "license": "MIT", "homepage": "https://github.com/zloirock/core-js", "repository": {"type": "git", "url": "https://github.com/zloirock/core-js.git"}, "ignore": ["build", "node_modules", "tests"]}