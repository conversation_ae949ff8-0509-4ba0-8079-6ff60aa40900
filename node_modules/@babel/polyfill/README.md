# @babel/polyfill

> Provides polyfills necessary for a full ES2015+ environment


**This package has been deprecated in favor of separate inclusion of required parts of [`core-js`](https://github.com/zloirock/core-js) and [`regenerator-runtime`](https://www.npmjs.com/package/regenerator-runtime). See our website [@babel/polyfill](https://babeljs.io/docs/en/next/babel-polyfill.html) for more information.**

See our website [@babel/polyfill](https://babeljs.io/docs/en/next/babel-polyfill.html) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20polyfill%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save @babel/polyfill
```

or using yarn:

```sh
yarn add @babel/polyfill 
```