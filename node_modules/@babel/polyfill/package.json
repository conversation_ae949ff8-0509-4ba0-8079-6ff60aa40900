{"name": "@babel/polyfill", "version": "7.12.1", "description": "Provides polyfills necessary for a full ES2015+ environment", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"prepublishOnly": "cp dist/polyfill.min.js browser.js", "postpublish": "rm browser.js"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-polyfill"}, "main": "lib/index.js", "dependencies": {"core-js": "^2.6.5", "regenerator-runtime": "^0.13.4"}, "devDependencies": {"browserify": "^16.5.2", "bundle-collapser": "1.3.0", "derequire": "2.1.1", "uglify-js": "3.7.2"}}