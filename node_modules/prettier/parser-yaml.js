!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(((e=e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.yaml={}))}(this,(function(e){"use strict";var t=function(e,t){var n=new SyntaxError(e+" ("+t.start.line+":"+t.start.column+")");return n.loc=t,n};var n=function(e){return/^\s*#[^\n\S]*@(prettier|format)\s*?(\n|$)/.test(e)},r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e,t){return e(t={exports:{}},t.exports),t.exports}function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function f(e,t,n){return t&&u(e.prototype,t),n&&u(e,n),e}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e,t){return(d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function h(e,t,n){return(h=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var a=new(Function.bind.apply(e,r));return n&&d(a,n.prototype),a}).apply(null,arguments)}function p(e){var t="function"==typeof Map?new Map:void 0;return(p=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return h(e,arguments,c(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),d(r,e)})(e)}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function g(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?v(e):t}function m(e,t,n){return(m="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}(e,t);if(r){var a=Object.getOwnPropertyDescriptor(r,t);return a.get?a.get.call(n):a.value}})(e,t,n||e)}function y(e,t){return b(e)||function(e,t){if(!(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e)))return;var n=[],r=!0,a=!1,o=void 0;try{for(var i,s=e[Symbol.iterator]();!(r=(i=s.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(e){a=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(a)throw o}}return n}(e,t)||w()}function _(e){return b(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||w()}function b(e){if(Array.isArray(e))return e}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0

  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.

  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** */var E=function(e,t){return(E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};var O=function(){return(O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function M(e){var t="function"==typeof Symbol&&e[Symbol.iterator],n=0;return t?t.call(e):{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}}}function S(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i}function A(e){return this instanceof A?(this.v=e,this):new A(e)}var k=Object.freeze({__proto__:null,__extends:function(e,t){function n(){this.constructor=e}E(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},get __assign(){return O},__rest:function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n},__decorate:function(e,t,n,r){var a,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===("undefined"==typeof Reflect?"undefined":i(Reflect))&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var u=e.length-1;u>=0;u--)(a=e[u])&&(s=(o<3?a(s):o>3?a(t,n,s):a(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},__param:function(e,t){return function(n,r){t(n,r,e)}},__metadata:function(e,t){if("object"===("undefined"==typeof Reflect?"undefined":i(Reflect))&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},__awaiter:function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{u(r.next(e))}catch(e){o(e)}}function s(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){e.done?a(e.value):new n((function(t){t(e.value)})).then(i,s)}u((r=r.apply(e,t||[])).next())}))},__generator:function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=(a=i.trys).length>0&&a[a.length-1])&&(6===o[0]||2===o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},__exportStar:function(e,t){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])},__values:M,__read:S,__spread:function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(S(arguments[t]));return e},__spreadArrays:function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),a=0;for(t=0;t<n;t++)for(var o=arguments[t],i=0,s=o.length;i<s;i++,a++)r[a]=o[i];return r},__await:A,__asyncGenerator:function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,a=n.apply(e,t||[]),o=[];return r={},i("next"),i("throw"),i("return"),r[Symbol.asyncIterator]=function(){return this},r;function i(e){a[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||s(e,t)}))})}function s(e,t){try{(n=a[e](t)).value instanceof A?Promise.resolve(n.value.v).then(u,f):l(o[0][2],n)}catch(e){l(o[0][3],e)}var n}function u(e){s("next",e)}function f(e){s("throw",e)}function l(e,t){e(t),o.shift(),o.length&&s(o[0][0],o[0][1])}},__asyncDelegator:function(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,a){t[r]=e[r]?function(t){return(n=!n)?{value:A(e[r](t)),done:"return"===r}:a?a(t):t}:a}},__asyncValues:function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=M(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,a){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,a,(t=e[n](t)).done,t.value)}))}}},__makeTemplateObject:function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},__importStar:function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t},__importDefault:function(e){return e&&e.__esModule?e:{default:e}}}),L=o((function(e,t){var n="\n",r="\r",a=function(){function e(e){this.string=e;for(var t=[0],a=0;a<e.length;)switch(e[a]){case n:a+=n.length,t.push(a);break;case r:e[a+=r.length]===n&&(a+=n.length),t.push(a);break;default:a++}this.offsets=t}return e.prototype.locationForIndex=function(e){if(e<0||e>this.string.length)return null;for(var t=0,n=this.offsets;n[t+1]<=e;)t++;return{line:t,column:e-n[t]}},e.prototype.indexForLocation=function(e){var t=e.line,n=e.column;return t<0||t>=this.offsets.length?null:n<0||n>this.lengthOfLine(t)?null:this.offsets[t]+n},e.prototype.lengthOfLine=function(e){var t=this.offsets[e];return(e===this.offsets.length-1?this.string.length:this.offsets[e+1])-t},e}();t.__esModule=!0,t.default=a}));a(L);var T=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Type=t.Char=void 0;t.Char={ANCHOR:"&",COMMENT:"#",TAG:"!",DIRECTIVES_END:"-",DOCUMENT_END:"."};t.Type={ALIAS:"ALIAS",BLANK_LINE:"BLANK_LINE",BLOCK_FOLDED:"BLOCK_FOLDED",BLOCK_LITERAL:"BLOCK_LITERAL",COMMENT:"COMMENT",DIRECTIVE:"DIRECTIVE",DOCUMENT:"DOCUMENT",FLOW_MAP:"FLOW_MAP",FLOW_SEQ:"FLOW_SEQ",MAP:"MAP",MAP_KEY:"MAP_KEY",MAP_VALUE:"MAP_VALUE",PLAIN:"PLAIN",QUOTE_DOUBLE:"QUOTE_DOUBLE",QUOTE_SINGLE:"QUOTE_SINGLE",SEQ:"SEQ",SEQ_ITEM:"SEQ_ITEM"}}));a(T);T.Type,T.Char;var P=o((function(e,t){function n(e){for(var t=[0],n=e.indexOf("\n");-1!==n;)n+=1,t.push(n),n=e.indexOf("\n",n);return t}function r(e){var t,r;return"string"==typeof e?(t=n(e),r=e):(Array.isArray(e)&&(e=e[0]),e&&e.context&&(e.lineStarts||(e.lineStarts=n(e.context.src)),t=e.lineStarts,r=e.context.src)),{lineStarts:t,src:r}}function a(e,t){var n=r(t),a=n.lineStarts,o=n.src;if(!a||!(e>=1)||e>a.length)return null;for(var i=a[e-1],s=a[e];s&&s>i&&"\n"===o[s-1];)--s;return o.slice(i,s)}Object.defineProperty(t,"__esModule",{value:!0}),t.getLinePos=function(e,t){if("number"!=typeof e||e<0)return null;var n=r(t),a=n.lineStarts,o=n.src;if(!a||!o||e>o.length)return null;for(var i=0;i<a.length;++i){var s=a[i];if(e<s)return{line:i,col:e-a[i-1]+1};if(e===s)return{line:i+1,col:1}}var u=a.length;return{line:u,col:e-a[u-1]+1}},t.getLine=a,t.getPrettyContext=function(e,t){var n=e.start,r=e.end,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:80,i=a(n.line,t);if(!i)return null;var s=n.col;if(i.length>o)if(s<=o-10)i=i.substr(0,o-1)+"…";else{var u=Math.round(o/2);i.length>s+u&&(i=i.substr(0,s+u-1)+"…"),s-=i.length-o,i="…"+i.substr(1-o)}var f=1,l="";r&&(r.line===n.line&&s+(r.col-n.col)<=o+1?f=r.col-n.col:(f=Math.min(i.length+1,o)-s,l="…"));var c=s>1?" ".repeat(s-1):"",d="^".repeat(f);return"".concat(i,"\n").concat(c).concat(d).concat(l)}}));a(P);P.getLinePos,P.getLine,P.getPrettyContext;var C=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(){function e(t,n){s(this,e),this.start=t,this.end=n||t}return f(e,null,[{key:"copy",value:function(t){return new e(t.start,t.end)}}]),f(e,[{key:"isEmpty",value:function(){return"number"!=typeof this.start||!this.end||this.end<=this.start}},{key:"setOrigRange",value:function(e,t){var n=this.start,r=this.end;if(0===e.length||r<=e[0])return this.origStart=n,this.origEnd=r,t;for(var a=t;a<e.length&&!(e[a]>n);)++a;this.origStart=n+a;for(var o=a;a<e.length&&!(e[a]>=r);)++a;return this.origEnd=r+a,o}}]),e}();t.default=n}));a(C);var N=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,r=(n=C)&&n.__esModule?n:{default:n};var a=function(){function e(t,n,r){s(this,e),Object.defineProperty(this,"context",{value:r||null,writable:!0}),this.error=null,this.range=null,this.valueRange=null,this.props=n||[],this.type=t,this.value=null}return f(e,null,[{key:"addStringTerminator",value:function(t,n,r){if("\n"===r[r.length-1])return r;var a=e.endOfWhiteSpace(t,n);return a>=t.length||"\n"===t[a]?r+"\n":r}},{key:"atDocumentBoundary",value:function(e,t,n){var r=e[t];if(!r)return!0;var a=e[t-1];if(a&&"\n"!==a)return!1;if(n){if(r!==n)return!1}else if(r!==T.Char.DIRECTIVES_END&&r!==T.Char.DOCUMENT_END)return!1;var o=e[t+1],i=e[t+2];if(o!==r||i!==r)return!1;var s=e[t+3];return!s||"\n"===s||"\t"===s||" "===s}},{key:"endOfIdentifier",value:function(e,t){for(var n=e[t],r="<"===n,a=r?["\n","\t"," ",">"]:["\n","\t"," ","[","]","{","}",","];n&&-1===a.indexOf(n);)n=e[t+=1];return r&&">"===n&&(t+=1),t}},{key:"endOfIndent",value:function(e,t){for(var n=e[t];" "===n;)n=e[t+=1];return t}},{key:"endOfLine",value:function(e,t){for(var n=e[t];n&&"\n"!==n;)n=e[t+=1];return t}},{key:"endOfWhiteSpace",value:function(e,t){for(var n=e[t];"\t"===n||" "===n;)n=e[t+=1];return t}},{key:"startOfLine",value:function(e,t){var n=e[t-1];if("\n"===n)return t;for(;n&&"\n"!==n;)n=e[t-=1];return t+1}},{key:"endOfBlockIndent",value:function(t,n,r){var a=e.endOfIndent(t,r);if(a>r+n)return a;var o=e.endOfWhiteSpace(t,a),i=t[o];return i&&"\n"!==i?null:o}},{key:"atBlank",value:function(e,t,n){var r=e[t];return"\n"===r||"\t"===r||" "===r||n&&!r}},{key:"atCollectionItem",value:function(t,n){var r=t[n];return("?"===r||":"===r||"-"===r)&&e.atBlank(t,n+1,!0)}},{key:"nextNodeIsIndented",value:function(e,t,n){return!(!e||t<0)&&(t>0||n&&"-"===e)}},{key:"normalizeOffset",value:function(t,n){var r=t[n];return r?"\n"!==r&&"\n"===t[n-1]?n-1:e.endOfWhiteSpace(t,n):n}},{key:"foldNewline",value:function(t,n,r){for(var a=0,o=!1,i="",s=t[n+1];" "===s||"\t"===s||"\n"===s;){switch(s){case"\n":a=0,n+=1,i+="\n";break;case"\t":a<=r&&(o=!0),n=e.endOfWhiteSpace(t,n+2)-1;break;case" ":a+=1,n+=1}s=t[n+1]}return i||(i=" "),s&&a<=r&&(o=!0),{fold:i,offset:n,error:o}}}]),f(e,[{key:"getPropValue",value:function(e,t,n){if(!this.context)return null;var r=this.context.src,a=this.props[e];return a&&r[a.start]===t?r.slice(a.start+(n?1:0),a.end):null}},{key:"commentHasRequiredWhitespace",value:function(t){var n=this.context.src;if(this.header&&t===this.header.end)return!1;if(!this.valueRange)return!1;var r=this.valueRange.end;return t!==r||e.atBlank(n,r-1)}},{key:"parseComment",value:function(t){var n=this.context.src;if(n[t]===T.Char.COMMENT){var a=e.endOfLine(n,t+1),o=new r.default(t,a);return this.props.push(o),a}return t}},{key:"setOrigRanges",value:function(e,t){return this.range&&(t=this.range.setOrigRange(e,t)),this.valueRange&&this.valueRange.setOrigRange(e,t),this.props.forEach((function(n){return n.setOrigRange(e,t)})),t}},{key:"toString",value:function(){var t=this.context.src,n=this.range,r=this.value;if(null!=r)return r;var a=t.slice(n.start,n.end);return e.addStringTerminator(t,n.end,a)}},{key:"anchor",get:function(){for(var e=0;e<this.props.length;++e){var t=this.getPropValue(e,T.Char.ANCHOR,!0);if(null!=t)return t}return null}},{key:"comment",get:function(){for(var e=[],t=0;t<this.props.length;++t){var n=this.getPropValue(t,T.Char.COMMENT,!0);null!=n&&e.push(n)}return e.length>0?e.join("\n"):null}},{key:"hasComment",get:function(){if(this.context)for(var e=this.context.src,t=0;t<this.props.length;++t)if(e[this.props[t].start]===T.Char.COMMENT)return!0;return!1}},{key:"hasProps",get:function(){if(this.context)for(var e=this.context.src,t=0;t<this.props.length;++t)if(e[this.props[t].start]!==T.Char.COMMENT)return!0;return!1}},{key:"includesTrailingLines",get:function(){return!1}},{key:"jsonLike",get:function(){return-1!==[T.Type.FLOW_MAP,T.Type.FLOW_SEQ,T.Type.QUOTE_DOUBLE,T.Type.QUOTE_SINGLE].indexOf(this.type)}},{key:"rangeAsLinePos",get:function(){if(this.range&&this.context){var e=(0,P.getLinePos)(this.range.start,this.context.root);if(e)return{start:e,end:(0,P.getLinePos)(this.range.end,this.context.root)}}}},{key:"rawValue",get:function(){if(!this.valueRange||!this.context)return null;var e=this.valueRange,t=e.start,n=e.end;return this.context.src.slice(t,n)}},{key:"tag",get:function(){for(var e=0;e<this.props.length;++e){var t=this.getPropValue(e,T.Char.TAG,!1);if(null!=t){if("<"===t[1])return{verbatim:t.slice(2,-1)};var n=y(t.match(/^(.*!)([^!]*)$/),3);n[0];return{handle:n[1],suffix:n[2]}}}return null}},{key:"valueRangeContainsNewline",get:function(){if(!this.valueRange||!this.context)return!1;for(var e=this.valueRange,t=e.start,n=e.end,r=this.context.src,a=t;a<n;++a)if("\n"===r[a])return!0;return!1}}]),e}();t.default=a}));a(N);var x=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.YAMLWarning=t.YAMLSyntaxError=t.YAMLSemanticError=t.YAMLReferenceError=t.YAMLError=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(e,r,a){var o;if(s(this,t),!(a&&r instanceof n.default))throw new Error("Invalid arguments for new ".concat(e));return(o=g(this,c(t).call(this))).name=e,o.message=a,o.source=r,o}return l(t,e),f(t,[{key:"makePretty",value:function(){if(this.source){this.nodeType=this.source.type;var e=this.source.context&&this.source.context.root;if("number"==typeof this.offset){this.range=new r.default(this.offset,this.offset+1);var t=e&&(0,P.getLinePos)(this.offset,e);if(t){var n={line:t.line,col:t.col+1};this.linePos={start:t,end:n}}delete this.offset}else this.range=this.source.range,this.linePos=this.source.rangeAsLinePos;if(this.linePos){var a=this.linePos.start,o=a.line,i=a.col;this.message+=" at line ".concat(o,", column ").concat(i);var s=e&&(0,P.getPrettyContext)(this.linePos,e);s&&(this.message+=":\n\n".concat(s,"\n"))}delete this.source}}}]),t}(p(Error));t.YAMLError=o;var i=function(e){function t(e,n){return s(this,t),g(this,c(t).call(this,"YAMLReferenceError",e,n))}return l(t,e),t}(o);t.YAMLReferenceError=i;var u=function(e){function t(e,n){return s(this,t),g(this,c(t).call(this,"YAMLSemanticError",e,n))}return l(t,e),t}(o);t.YAMLSemanticError=u;var d=function(e){function t(e,n){return s(this,t),g(this,c(t).call(this,"YAMLSyntaxError",e,n))}return l(t,e),t}(o);t.YAMLSyntaxError=d;var h=function(e){function t(e,n){return s(this,t),g(this,c(t).call(this,"YAMLWarning",e,n))}return l(t,e),t}(o);t.YAMLWarning=h}));a(x);x.YAMLWarning,x.YAMLSyntaxError,x.YAMLSemanticError,x.YAMLReferenceError,x.YAMLError;var R=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).call(this,T.Type.BLANK_LINE))}return l(t,e),f(t,[{key:"parse",value:function(e,t){this.context=e;for(var a=e.src,o=t+1;n.default.atBlank(a,o);){var i=n.default.endOfWhiteSpace(a,o);if("\n"!==i)break;o=i+1}return this.range=new r.default(t,o),o}},{key:"includesTrailingLines",get:function(){return!0}}]),t}(n.default);t.default=o}));a(R);var I=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(R),r=o(N),a=o(C);function o(e){return e&&e.__esModule?e:{default:e}}var i=function(e){function t(e,n){var r;return s(this,t),(r=g(this,c(t).call(this,e,n))).node=null,r}return l(t,e),f(t,[{key:"parse",value:function(e,t){this.context=e;var o=e.parseNode,i=e.src,s=e.atLineStart,u=e.lineStart;s||this.type!==T.Type.SEQ_ITEM||(this.error=new x.YAMLSemanticError(this,"Sequence items must not have preceding content on the same line"));for(var f=s?t-u:e.indent,l=r.default.endOfWhiteSpace(i,t+1),c=i[l],d="#"===c,h=[],p=null;"\n"===c||"#"===c;){if("#"===c){var v=r.default.endOfLine(i,l+1);h.push(new a.default(l,v)),l=v}else{s=!0,u=l+1,"\n"===i[r.default.endOfWhiteSpace(i,u)]&&0===h.length&&(u=(p=new n.default).parse({src:i},u)),l=r.default.endOfIndent(i,u)}c=i[l]}if(r.default.nextNodeIsIndented(c,l-(u+f),this.type!==T.Type.SEQ_ITEM)?this.node=o({atLineStart:s,inCollection:!1,indent:f,lineStart:u,parent:this},l):c&&u>t+1&&(l=u-1),this.node){if(p){var g=e.parent.items||e.parent.contents;g&&g.push(p)}h.length&&Array.prototype.push.apply(this.props,h),l=this.node.range.end}else if(d){var m=h[0];this.props.push(m),l=m.end}else l=r.default.endOfLine(i,t+1);var y=this.node?this.node.valueRange.end:l;return this.valueRange=new a.default(t,y),l}},{key:"setOrigRanges",value:function(e,n){return n=m(c(t.prototype),"setOrigRanges",this).call(this,e,n),this.node?this.node.setOrigRanges(e,n):n}},{key:"toString",value:function(){var e=this.context.src,t=this.node,n=this.range,a=this.value;if(null!=a)return a;var o=t?e.slice(n.start,t.range.start)+String(t):e.slice(n.start,n.end);return r.default.addStringTerminator(e,n.end,o)}},{key:"includesTrailingLines",get:function(){return!!this.node&&this.node.includesTrailingLines}}]),t}(r.default);t.default=i}));a(I);var B=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).call(this,T.Type.COMMENT))}return l(t,e),f(t,[{key:"parse",value:function(e,t){this.context=e;var n=this.parseComment(t);return this.range=new r.default(t,n),n}}]),t}(n.default);t.default=o}));a(B);var D=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.grabCollectionEndComments=d,t.default=void 0;var n=u(R),r=u(I),a=u(B),o=u(N),i=u(C);function u(e){return e&&e.__esModule?e:{default:e}}function d(e){for(var t=e;t instanceof r.default;)t=t.node;if(!(t instanceof h))return null;for(var n=t.items.length,a=-1,o=n-1;o>=0;--o){var i=t.items[o];if(i.type===T.Type.COMMENT){var s=i.context,u=s.indent,f=s.lineStart;if(u>0&&i.range.start>=f+u)break;a=o}else{if(i.type!==T.Type.BLANK_LINE)break;a=o}}if(-1===a)return null;for(var l=t.items.splice(a,n-a),c=l[0].range.start;t.range.end=c,t.valueRange&&t.valueRange.end>c&&(t.valueRange.end=c),t!==e;)t=t.context.parent;return l}var h=function(e){function t(e){var n;s(this,t),n=g(this,c(t).call(this,e.type===T.Type.SEQ_ITEM?T.Type.SEQ:T.Type.MAP));for(var r=e.props.length-1;r>=0;--r)if(e.props[r].start<e.context.lineStart){n.props=e.props.slice(0,r+1),e.props=e.props.slice(r+1);var a=e.props[0]||e.valueRange;e.range.start=a.start;break}n.items=[e];var o=d(e);return o&&Array.prototype.push.apply(n.items,o),n}return l(t,e),f(t,null,[{key:"nextContentHasIndent",value:function(e,n,r){var a=o.default.endOfLine(e,n)+1,i=e[n=o.default.endOfWhiteSpace(e,a)];return!!i&&(n>=a+r||("#"===i||"\n"===i)&&t.nextContentHasIndent(e,n,r))}}]),f(t,[{key:"parse",value:function(e,r){this.context=e;var s=e.parseNode,u=e.src,f=o.default.startOfLine(u,r),l=this.items[0];l.context.parent=this,this.valueRange=i.default.copy(l.valueRange);for(var c=l.range.start-l.context.lineStart,h=r,p=u[h=o.default.normalizeOffset(u,h)],v=o.default.endOfWhiteSpace(u,f)===h,g=!1;p;){for(;"\n"===p||"#"===p;){if(v&&"\n"===p&&!g){var m=new n.default;if(h=m.parse({src:u},h),this.valueRange.end=h,h>=u.length){p=null;break}this.items.push(m),h-=1}else if("#"===p){if(h<f+c&&!t.nextContentHasIndent(u,h,c))return h;var y=new a.default;if(h=y.parse({indent:c,lineStart:f,src:u},h),this.items.push(y),this.valueRange.end=h,h>=u.length){p=null;break}}if(f=h+1,h=o.default.endOfIndent(u,f),o.default.atBlank(u,h)){var _=o.default.endOfWhiteSpace(u,h),b=u[_];b&&"\n"!==b&&"#"!==b||(h=_)}p=u[h],v=!0}if(!p)break;if(h!==f+c&&(v||":"!==p)){f>r&&(h=f);break}if(l.type===T.Type.SEQ_ITEM!=("-"===p)){var w=!0;if("-"===p){var E=u[h+1];w=!E||"\n"===E||"\t"===E||" "===E}if(w){f>r&&(h=f);break}}var O=s({atLineStart:v,inCollection:!0,indent:c,lineStart:f,parent:this},h);if(!O)return h;if(this.items.push(O),this.valueRange.end=O.valueRange.end,p=u[h=o.default.normalizeOffset(u,O.range.end)],v=!1,g=O.includesTrailingLines,p){for(var M=h-1,S=u[M];" "===S||"\t"===S;)S=u[--M];"\n"===S&&(f=M+1,v=!0)}var A=d(O);A&&Array.prototype.push.apply(this.items,A)}return h}},{key:"setOrigRanges",value:function(e,n){return n=m(c(t.prototype),"setOrigRanges",this).call(this,e,n),this.items.forEach((function(t){n=t.setOrigRanges(e,n)})),n}},{key:"toString",value:function(){var e=this.context.src,t=this.items,n=this.range,r=this.value;if(null!=r)return r;for(var a=e.slice(n.start,t[0].range.start)+String(t[0]),i=1;i<t.length;++i){var s=t[i],u=s.context,f=u.atLineStart,l=u.indent;if(f)for(var c=0;c<l;++c)a+=" ";a+=String(s)}return o.default.addStringTerminator(e,n.end,a)}},{key:"includesTrailingLines",get:function(){return this.items.length>0}}]),t}(o.default);t.default=h}));a(D);D.grabCollectionEndComments;var j=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){var e;return s(this,t),(e=g(this,c(t).call(this,T.Type.DIRECTIVE))).name=null,e}return l(t,e),f(t,null,[{key:"endOfDirective",value:function(e,t){for(var n=e[t];n&&"\n"!==n&&"#"!==n;)n=e[t+=1];for(n=e[t-1];" "===n||"\t"===n;)n=e[(t-=1)-1];return t}}]),f(t,[{key:"parseName",value:function(e){for(var t=this.context.src,n=e,r=t[n];r&&"\n"!==r&&"\t"!==r&&" "!==r;)r=t[n+=1];return this.name=t.slice(e,n),n}},{key:"parseParameters",value:function(e){for(var t=this.context.src,n=e,a=t[n];a&&"\n"!==a&&"#"!==a;)a=t[n+=1];return this.valueRange=new r.default(e,n),n}},{key:"parse",value:function(e,t){this.context=e;var n=this.parseName(t+1);return n=this.parseParameters(n),n=this.parseComment(n),this.range=new r.default(t,n),n}},{key:"parameters",get:function(){var e=this.rawValue;return e?e.trim().split(/[ \t]+/):[]}}]),t}(n.default);t.default=o}));a(j);var Y=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=u(R),r=u(B),a=u(j),o=u(N),i=u(C);function u(e){return e&&e.__esModule?e:{default:e}}var d=function(e){function t(){var e;return s(this,t),(e=g(this,c(t).call(this,T.Type.DOCUMENT))).directives=null,e.contents=null,e.directivesEndMarker=null,e.documentEndMarker=null,e}return l(t,e),f(t,null,[{key:"startCommentOrEndBlankLine",value:function(e,t){var n=o.default.endOfWhiteSpace(e,t),r=e[n];return"#"===r||"\n"===r?n:t}}]),f(t,[{key:"parseDirectives",value:function(e){var s=this.context.src;this.directives=[];for(var u=!0,f=!1,l=e;!o.default.atDocumentBoundary(s,l,T.Char.DIRECTIVES_END);)switch(s[l=t.startCommentOrEndBlankLine(s,l)]){case"\n":if(u){var c=new n.default;(l=c.parse({src:s},l))<s.length&&this.directives.push(c)}else l+=1,u=!0;break;case"#":var d=new r.default;l=d.parse({src:s},l),this.directives.push(d),u=!1;break;case"%":var h=new a.default;l=h.parse({parent:this,src:s},l),this.directives.push(h),f=!0,u=!1;break;default:return f?this.error=new x.YAMLSemanticError(this,"Missing directives-end indicator line"):this.directives.length>0&&(this.contents=this.directives,this.directives=[]),l}return s[l]?(this.directivesEndMarker=new i.default(l,l+3),l+3):(f?this.error=new x.YAMLSemanticError(this,"Missing directives-end indicator line"):this.directives.length>0&&(this.contents=this.directives,this.directives=[]),l)}},{key:"parseContents",value:function(e){var a=this.context,s=a.parseNode,u=a.src;this.contents||(this.contents=[]);for(var f=e;"-"===u[f-1];)f-=1;var l=o.default.endOfWhiteSpace(u,e),c=f===e;for(this.valueRange=new i.default(l);!o.default.atDocumentBoundary(u,l,T.Char.DOCUMENT_END);){switch(u[l]){case"\n":if(c){var d=new n.default;(l=d.parse({src:u},l))<u.length&&this.contents.push(d)}else l+=1,c=!0;f=l;break;case"#":var h=new r.default;l=h.parse({src:u},l),this.contents.push(h),c=!1;break;default:var p=o.default.endOfIndent(u,l),v=s({atLineStart:c,indent:-1,inFlow:!1,inCollection:!1,lineStart:f,parent:this},p);if(!v)return this.valueRange.end=p;this.contents.push(v),l=v.range.end,c=!1;var g=(0,D.grabCollectionEndComments)(v);g&&Array.prototype.push.apply(this.contents,g)}l=t.startCommentOrEndBlankLine(u,l)}if(this.valueRange.end=l,u[l]&&(this.documentEndMarker=new i.default(l,l+3),u[l+=3])){if("#"===u[l=o.default.endOfWhiteSpace(u,l)]){var m=new r.default;l=m.parse({src:u},l),this.contents.push(m)}switch(u[l]){case"\n":l+=1;break;case void 0:break;default:this.error=new x.YAMLSyntaxError(this,"Document end marker line cannot have a non-comment suffix")}}return l}},{key:"parse",value:function(e,t){e.root=this,this.context=e;var n=65279===e.src.charCodeAt(t)?t+1:t;return n=this.parseDirectives(n),n=this.parseContents(n)}},{key:"setOrigRanges",value:function(e,n){return n=m(c(t.prototype),"setOrigRanges",this).call(this,e,n),this.directives.forEach((function(t){n=t.setOrigRanges(e,n)})),this.directivesEndMarker&&(n=this.directivesEndMarker.setOrigRange(e,n)),this.contents.forEach((function(t){n=t.setOrigRanges(e,n)})),this.documentEndMarker&&(n=this.documentEndMarker.setOrigRange(e,n)),n}},{key:"toString",value:function(){var e=this.contents,t=this.directives,n=this.value;if(null!=n)return n;var r=t.join("");return e.length>0&&((t.length>0||e[0].type===T.Type.COMMENT)&&(r+="---\n"),r+=e.join("")),"\n"!==r[r.length-1]&&(r+="\n"),r}}]),t}(o.default);t.default=d}));a(Y);var F=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"parse",value:function(e,t){this.context=e;var a=e.src,o=n.default.endOfIdentifier(a,t+1);return this.valueRange=new r.default(t+1,o),o=n.default.endOfWhiteSpace(a,o),o=this.parseComment(o)}}]),t}(n.default);t.default=o}));a(F);var U=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Chomp=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o={CLIP:"CLIP",KEEP:"KEEP",STRIP:"STRIP"};t.Chomp=o;var i=function(e){function t(e,n){var r;return s(this,t),(r=g(this,c(t).call(this,e,n))).blockIndent=null,r.chomping=o.CLIP,r.header=null,r}return l(t,e),f(t,[{key:"parseBlockHeader",value:function(e){for(var t=this.context.src,n=e+1,a="";;){var i=t[n];switch(i){case"-":this.chomping=o.STRIP;break;case"+":this.chomping=o.KEEP;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":a+=i;break;default:return this.blockIndent=Number(a)||null,this.header=new r.default(e,n),n}n+=1}}},{key:"parseBlockValue",value:function(e){for(var t=this.context,a=t.indent,i=t.src,s=e,u=e,f=this.blockIndent?a+this.blockIndent-1:a,l=1,c=i[s];"\n"===c&&(s+=1,!n.default.atDocumentBoundary(i,s));c=i[s]){var d=n.default.endOfBlockIndent(i,f,s);if(null===d)break;if(!this.blockIndent){var h=d-(s+a);if("\n"!==i[d]){if(h<l){s-=1;break}this.blockIndent=h,f=a+this.blockIndent-1}else h>l&&(l=h)}s="\n"===i[d]?d:u=n.default.endOfLine(i,d)}return this.chomping!==o.KEEP&&(s=i[u]?u+1:u),this.valueRange=new r.default(e+1,s),s}},{key:"parse",value:function(e,t){this.context=e;var r=e.src,a=this.parseBlockHeader(t);return a=n.default.endOfWhiteSpace(r,a),a=this.parseComment(a),a=this.parseBlockValue(a)}},{key:"setOrigRanges",value:function(e,n){return n=m(c(t.prototype),"setOrigRanges",this).call(this,e,n),this.header?this.header.setOrigRange(e,n):n}},{key:"includesTrailingLines",get:function(){return this.chomping===o.KEEP}},{key:"strValue",get:function(){if(!this.valueRange||!this.context)return null;var e=this.valueRange,t=e.start,r=e.end,a=this.context,i=a.indent,s=a.src;if(this.valueRange.isEmpty())return"";for(var u=null,f=s[r-1];"\n"===f||"\t"===f||" "===f;){if((r-=1)<=t){if(this.chomping===o.KEEP)break;return""}"\n"===f&&(u=r),f=s[r-1]}var l=r+1;u&&(this.chomping===o.KEEP?(l=u,r=this.valueRange.end):r=u);for(var c=i+this.blockIndent,d=this.type===T.Type.BLOCK_FOLDED,h=!0,p="",v="",g=!1,m=t;m<r;++m){for(var y=0;y<c&&" "===s[m];++y)m+=1;var _=s[m];if("\n"===_)"\n"===v?p+="\n":v="\n";else{var b=n.default.endOfLine(s,m),w=s.slice(m,b);m=b,d&&(" "===_||"\t"===_)&&m<l?(" "===v?v="\n":g||h||"\n"!==v||(v="\n\n"),p+=v+w,v=b<r&&s[b]||"",g=!0):(p+=v+w,v=d&&m<l?" ":"\n",g=!1),h&&""!==w&&(h=!1)}}return this.chomping===o.STRIP?p:p+"\n"}}]),t}(n.default);t.default=i}));a(U);U.Chomp;var K=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(R),r=i(B),a=i(N),o=i(C);function i(e){return e&&e.__esModule?e:{default:e}}var u=function(e){function t(e,n){var r;return s(this,t),(r=g(this,c(t).call(this,e,n))).items=null,r}return l(t,e),f(t,[{key:"prevNodeIsJsonLike",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.items.length,t=this.items[e-1];return!!t&&(t.jsonLike||t.type===T.Type.COMMENT&&this.nodeIsJsonLike(e-1))}},{key:"parse",value:function(e,t){this.context=e;var i=e.parseNode,s=e.src,u=e.indent,f=e.lineStart,l=s[t];this.items=[{char:l,offset:t}];var c=a.default.endOfWhiteSpace(s,t+1);for(l=s[c];l&&"]"!==l&&"}"!==l;){switch(l){case"\n":if(f=c+1,"\n"===s[a.default.endOfWhiteSpace(s,f)]){var d=new n.default;f=d.parse({src:s},f),this.items.push(d)}if((c=a.default.endOfIndent(s,f))<=f+u&&(l=s[c],c<f+u||"]"!==l&&"}"!==l)){this.error=new x.YAMLSemanticError(this,"Insufficient indentation in flow collection")}break;case",":this.items.push({char:l,offset:c}),c+=1;break;case"#":var h=new r.default;c=h.parse({src:s},c),this.items.push(h);break;case"?":case":":var p=s[c+1];if("\n"===p||"\t"===p||" "===p||","===p||":"===l&&this.prevNodeIsJsonLike()){this.items.push({char:l,offset:c}),c+=1;break}default:var v=i({atLineStart:!1,inCollection:!1,inFlow:!0,indent:-1,lineStart:f,parent:this},c);if(!v)return this.valueRange=new o.default(t,c),c;this.items.push(v),c=a.default.normalizeOffset(s,v.range.end)}l=s[c=a.default.endOfWhiteSpace(s,c)]}return this.valueRange=new o.default(t,c+1),l&&(this.items.push({char:l,offset:c}),c=a.default.endOfWhiteSpace(s,c+1),c=this.parseComment(c)),c}},{key:"setOrigRanges",value:function(e,n){return n=m(c(t.prototype),"setOrigRanges",this).call(this,e,n),this.items.forEach((function(t){if(t instanceof a.default)n=t.setOrigRanges(e,n);else if(0===e.length)t.origOffset=t.offset;else{for(var r=n;r<e.length&&!(e[r]>t.offset);)++r;t.origOffset=t.offset+r,n=r}})),n}},{key:"toString",value:function(){var e=this.context.src,t=this.items,n=this.range,r=this.value;if(null!=r)return r;var o=t.filter((function(e){return e instanceof a.default})),i="",s=n.start;return o.forEach((function(t){var n=e.slice(s,t.range.start);s=t.range.end,"\n"===(i+=n+String(t))[i.length-1]&&"\n"!==e[s-1]&&"\n"===e[s]&&(s+=1)})),i+=e.slice(s,n.end),a.default.addStringTerminator(e,n.end,i)}}]),t}(a.default);t.default=u}));a(K);var W=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"parseBlockValue",value:function(e){for(var r=this.context,a=r.indent,o=r.inFlow,i=r.src,s=e,u=e,f=i[s];"\n"===f&&!n.default.atDocumentBoundary(i,s+1);f=i[s]){var l=n.default.endOfBlockIndent(i,a,s+1);if(null===l||"#"===i[l])break;s="\n"===i[l]?l:u=t.endOfLine(i,l,o)}return this.valueRange.isEmpty()&&(this.valueRange.start=e),this.valueRange.end=u,u}},{key:"parse",value:function(e,a){this.context=e;var o=e.inFlow,i=e.src,s=a,u=i[s];return u&&"#"!==u&&"\n"!==u&&(s=t.endOfLine(i,a,o)),this.valueRange=new r.default(a,s),s=n.default.endOfWhiteSpace(i,s),s=this.parseComment(s),this.hasComment&&!this.valueRange.isEmpty()||(s=this.parseBlockValue(s)),s}},{key:"strValue",get:function(){if(!this.valueRange||!this.context)return null;for(var e=this.valueRange,t=e.start,r=e.end,a=this.context.src,o=a[r-1];t<r&&("\n"===o||"\t"===o||" "===o);)o=a[--r-1];for(o=a[t];t<r&&("\n"===o||"\t"===o||" "===o);)o=a[++t];for(var i="",s=t;s<r;++s){var u=a[s];if("\n"===u){var f=n.default.foldNewline(a,s,-1);i+=f.fold,s=f.offset}else if(" "===u||"\t"===u){for(var l=s,c=a[s+1];s<r&&(" "===c||"\t"===c);)c=a[(s+=1)+1];"\n"!==c&&(i+=s>l?a.slice(l,s+1):u)}else i+=u}return i}}],[{key:"endOfLine",value:function(e,t,n){for(var r=e[t],a=t;r&&"\n"!==r&&(!n||"["!==r&&"]"!==r&&"{"!==r&&"}"!==r&&","!==r);){var o=e[a+1];if(":"===r&&(!o||"\n"===o||"\t"===o||" "===o||n&&","===o))break;if((" "===r||"\t"===r)&&"#"===o)break;a+=1,r=o}return a}}]),t}(n.default);t.default=o}));a(W);var Q=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"parseCharCode",value:function(e,t,n){var r=this.context.src,a=r.substr(e,t),o=a.length===t&&/^[0-9a-fA-F]+$/.test(a)?parseInt(a,16):NaN;return isNaN(o)?(n.push(new x.YAMLSyntaxError(this,"Invalid escape sequence ".concat(r.substr(e-2,t+2)))),r.substr(e-2,t+2)):String.fromCodePoint(o)}},{key:"parse",value:function(e,a){this.context=e;var o=e.src,i=t.endOfQuote(o,a+1);return this.valueRange=new r.default(a,i),i=n.default.endOfWhiteSpace(o,i),i=this.parseComment(i)}},{key:"strValue",get:function(){if(!this.valueRange||!this.context)return null;var e=[],t=this.valueRange,r=t.start,a=t.end,o=this.context,i=o.indent,s=o.src;'"'!==s[a-1]&&e.push(new x.YAMLSyntaxError(this,'Missing closing "quote'));for(var u="",f=r+1;f<a-1;++f){var l=s[f];if("\n"===l){n.default.atDocumentBoundary(s,f+1)&&e.push(new x.YAMLSemanticError(this,"Document boundary indicators are not allowed within string values"));var c=n.default.foldNewline(s,f,i);u+=c.fold,f=c.offset,c.error&&e.push(new x.YAMLSemanticError(this,"Multi-line double-quoted string needs to be sufficiently indented"))}else if("\\"===l)switch(s[f+=1]){case"0":u+="\0";break;case"a":u+="";break;case"b":u+="\b";break;case"e":u+="";break;case"f":u+="\f";break;case"n":u+="\n";break;case"r":u+="\r";break;case"t":u+="\t";break;case"v":u+="\v";break;case"N":u+="";break;case"_":u+=" ";break;case"L":u+="\u2028";break;case"P":u+="\u2029";break;case" ":u+=" ";break;case'"':u+='"';break;case"/":u+="/";break;case"\\":u+="\\";break;case"\t":u+="\t";break;case"x":u+=this.parseCharCode(f+1,2,e),f+=2;break;case"u":u+=this.parseCharCode(f+1,4,e),f+=4;break;case"U":u+=this.parseCharCode(f+1,8,e),f+=8;break;case"\n":for(;" "===s[f+1]||"\t"===s[f+1];)f+=1;break;default:e.push(new x.YAMLSyntaxError(this,"Invalid escape sequence ".concat(s.substr(f-1,2)))),u+="\\"+s[f]}else if(" "===l||"\t"===l){for(var d=f,h=s[f+1];" "===h||"\t"===h;)h=s[(f+=1)+1];"\n"!==h&&(u+=f>d?s.slice(d,f+1):l)}else u+=l}return e.length>0?{errors:e,str:u}:u}}],[{key:"endOfQuote",value:function(e,t){for(var n=e[t];n&&'"'!==n;)n=e[t+="\\"===n?2:1];return t+1}}]),t}(n.default);t.default=o}));a(Q);var V=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(N),r=a(C);function a(e){return e&&e.__esModule?e:{default:e}}var o=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"parse",value:function(e,a){this.context=e;var o=e.src,i=t.endOfQuote(o,a+1);return this.valueRange=new r.default(a,i),i=n.default.endOfWhiteSpace(o,i),i=this.parseComment(i)}},{key:"strValue",get:function(){if(!this.valueRange||!this.context)return null;var e=[],t=this.valueRange,r=t.start,a=t.end,o=this.context,i=o.indent,s=o.src;"'"!==s[a-1]&&e.push(new x.YAMLSyntaxError(this,"Missing closing 'quote"));for(var u="",f=r+1;f<a-1;++f){var l=s[f];if("\n"===l){n.default.atDocumentBoundary(s,f+1)&&e.push(new x.YAMLSemanticError(this,"Document boundary indicators are not allowed within string values"));var c=n.default.foldNewline(s,f,i);u+=c.fold,f=c.offset,c.error&&e.push(new x.YAMLSemanticError(this,"Multi-line single-quoted string needs to be sufficiently indented"))}else if("'"===l)u+=l,"'"!==s[f+=1]&&e.push(new x.YAMLSyntaxError(this,"Unescaped single quote? This should not happen."));else if(" "===l||"\t"===l){for(var d=f,h=s[f+1];" "===h||"\t"===h;)h=s[(f+=1)+1];"\n"!==h&&(u+=f>d?s.slice(d,f+1):l)}else u+=l}return e.length>0?{errors:e,str:u}:u}}],[{key:"endOfQuote",value:function(e,t){for(var n=e[t];n;)if("'"===n){if("'"!==e[t+1])break;n=e[t+=2]}else n=e[t+=1];return t+1}}]),t}(n.default);t.default=o}));a(V);var $=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=p(F),r=p(U),a=p(D),o=p(I),i=p(K),u=p(N),l=p(W),c=p(Q),d=p(V),h=p(C);function p(e){return e&&e.__esModule?e:{default:e}}var v=function(){function e(){var t,f,p,v=this,g=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},y=m.atLineStart,_=m.inCollection,b=m.inFlow,w=m.indent,E=m.lineStart,O=m.parent;s(this,e),p=function(t,s){if(u.default.atDocumentBoundary(v.src,s))return null;var f,p=new e(v,t),g=p.parseProps(s),m=g.props,y=g.type,_=g.valueStart;switch(y){case T.Type.ALIAS:f=new n.default(y,m);break;case T.Type.BLOCK_FOLDED:case T.Type.BLOCK_LITERAL:f=new r.default(y,m);break;case T.Type.FLOW_MAP:case T.Type.FLOW_SEQ:f=new i.default(y,m);break;case T.Type.MAP_KEY:case T.Type.MAP_VALUE:case T.Type.SEQ_ITEM:f=new o.default(y,m);break;case T.Type.COMMENT:case T.Type.PLAIN:f=new l.default(y,m);break;case T.Type.QUOTE_DOUBLE:f=new c.default(y,m);break;case T.Type.QUOTE_SINGLE:f=new d.default(y,m);break;default:return f.error=new x.YAMLSyntaxError(f,"Unknown node type: ".concat(JSON.stringify(y))),f.range=new h.default(s,s+1),f}var b=f.parse(p,_);if(f.range=new h.default(s,b),b<=s&&(f.error=new Error("Node#parse consumed no characters"),f.error.parseEnd=b,f.error.source=f,f.range.end=s+1),p.nodeStartsCollection(f)){f.error||p.atLineStart||p.parent.type!==T.Type.DOCUMENT||(f.error=new x.YAMLSyntaxError(f,"Block collection must not have preceding content here (e.g. directives-end indicator)"));var w=new a.default(f);return b=w.parse(new e(p),b),w.range=new h.default(s,b),w}return f},(f="parseNode")in(t=this)?Object.defineProperty(t,f,{value:p,enumerable:!0,configurable:!0,writable:!0}):t[f]=p,this.atLineStart=null!=y?y:g.atLineStart||!1,this.inCollection=null!=_?_:g.inCollection||!1,this.inFlow=null!=b?b:g.inFlow||!1,this.indent=null!=w?w:g.indent,this.lineStart=null!=E?E:g.lineStart,this.parent=null!=O?O:g.parent||{},this.root=g.root,this.src=g.src}return f(e,null,[{key:"parseType",value:function(e,t,n){switch(e[t]){case"*":return T.Type.ALIAS;case">":return T.Type.BLOCK_FOLDED;case"|":return T.Type.BLOCK_LITERAL;case"{":return T.Type.FLOW_MAP;case"[":return T.Type.FLOW_SEQ;case"?":return!n&&u.default.atBlank(e,t+1,!0)?T.Type.MAP_KEY:T.Type.PLAIN;case":":return!n&&u.default.atBlank(e,t+1,!0)?T.Type.MAP_VALUE:T.Type.PLAIN;case"-":return!n&&u.default.atBlank(e,t+1,!0)?T.Type.SEQ_ITEM:T.Type.PLAIN;case'"':return T.Type.QUOTE_DOUBLE;case"'":return T.Type.QUOTE_SINGLE;default:return T.Type.PLAIN}}}]),f(e,[{key:"nodeStartsCollection",value:function(e){var t=this.inCollection,n=this.inFlow,r=this.src;if(t||n)return!1;if(e instanceof o.default)return!0;var a=e.range.end;return"\n"!==r[a]&&"\n"!==r[a-1]&&":"===r[a=u.default.endOfWhiteSpace(r,a)]}},{key:"parseProps",value:function(t){for(var n=this.inFlow,r=this.parent,a=this.src,o=[],i=!1,s=a[t=u.default.endOfWhiteSpace(a,t)];s===T.Char.ANCHOR||s===T.Char.COMMENT||s===T.Char.TAG||"\n"===s;){if("\n"===s){var f=t+1,l=u.default.endOfIndent(a,f),c=l-(f+this.indent),d=r.type===T.Type.SEQ_ITEM&&r.context.atLineStart;if(!u.default.nextNodeIsIndented(a[l],c,!d))break;this.atLineStart=!0,this.lineStart=f,i=!1,t=l}else if(s===T.Char.COMMENT){var p=u.default.endOfLine(a,t+1);o.push(new h.default(t,p)),t=p}else{var v=u.default.endOfIdentifier(a,t+1);s===T.Char.TAG&&","===a[v]&&/^[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+,\d\d\d\d(-\d\d){0,2}\/\S/.test(a.slice(t+1,v+13))&&(v=u.default.endOfIdentifier(a,v+5)),o.push(new h.default(t,v)),i=!0,t=u.default.endOfWhiteSpace(a,v)}s=a[t]}return i&&":"===s&&u.default.atBlank(a,t+1,!0)&&(t-=1),{props:o,type:e.parseType(a,t,n),valueStart:t}}},{key:"pretty",get:function(){var e={start:"".concat(this.lineStart," + ").concat(this.indent),in:[],parent:this.parent.type};return this.atLineStart||(e.start+=" + N"),this.inCollection&&e.in.push("collection"),this.inFlow&&e.in.push("flow"),e}}]),e}();t.default=v}));a($);var q=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=[];-1!==e.indexOf("\r")&&(e=e.replace(/\r\n?/g,(function(e,n){return e.length>1&&t.push(n),"\n"})));var a=[],o=0;do{var i=new n.default,s=new r.default({src:e});o=i.parse(s,o),a.push(i)}while(o<e.length);return a.setOrigRanges=function(){if(0===t.length)return!1;for(var e=1;e<t.length;++e)t[e]-=e;for(var n=0,r=0;r<a.length;++r)n=a[r].setOrigRanges(t,n);return t.splice(0,t.length),!0},a.toString=function(){return a.join("...\n")},a};var n=a(Y),r=a($);function a(e){return e&&e.__esModule?e:{default:e}}}));a(q);var J=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.addCommentBefore=function(e,t,n){if(!n)return e;var r=n.replace(/[\s\S]^/gm,"$&".concat(t,"#"));return"#".concat(r,"\n").concat(t).concat(e)},t.default=function(e,t,n){return n?-1===n.indexOf("\n")?"".concat(e," #").concat(n):"".concat(e,"\n")+n.replace(/^/gm,"".concat(t||"","#")):e}}));a(J);J.addCommentBefore;var G=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,n,r){if(Array.isArray(t))return t.map((function(t,n){return e(t,String(n),r)}));if(t&&"function"==typeof t.toJSON){var a=r&&r.anchors&&r.anchors.find((function(e){return e.node===t}));a&&(r.onCreate=function(e){a.res=e,delete r.onCreate});var o=t.toJSON(n,r);return a&&r.onCreate&&r.onCreate(o),o}return t}}));a(G);var H=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function e(){s(this,e)}}));a(H);var z=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(G);function r(e){return e&&e.__esModule?e:{default:e}}var a=function(e){function t(e){var n;return s(this,t),(n=g(this,c(t).call(this))).value=e,n}return l(t,e),f(t,[{key:"toJSON",value:function(e,t){return t&&t.keep?this.value:(0,n.default)(this.value,e,t)}},{key:"toString",value:function(){return String(this.value)}}]),t}(r(H).default);t.default=a}));a(z);var Z=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=d(J),r=d(G),a=d(X),o=d(H),u=d(z);function d(e){return e&&e.__esModule?e:{default:e}}var h=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return s(this,t),(n=g(this,c(t).call(this))).key=e,n.value=r,n.type="PAIR",n}return l(t,e),f(t,[{key:"addToJSMap",value:function(e,t){var n=(0,r.default)(this.key,"",e);if(t instanceof Map){var a=(0,r.default)(this.value,n,e);t.set(n,a)}else if(t instanceof Set)t.add(n);else{var s=function(e,t,n){return null===t?"":"object"!==i(t)?String(t):e instanceof o.default&&n&&n.doc?e.toString({anchors:{},doc:n.doc,indent:"",inFlow:!0,inStringifyKey:!0}):JSON.stringify(t)}(this.key,n,e);t[s]=(0,r.default)(this.value,s,e)}return t}},{key:"toJSON",value:function(e,t){var n=t&&t.mapAsMap?new Map:{};return this.addToJSMap(t,n)}},{key:"toString",value:function(e,t,r){if(!e||!e.doc)return JSON.stringify(this);var s=e.doc.options.simpleKeys,u=this.key,f=this.value,l=u instanceof o.default&&u.comment;if(s){if(l)throw new Error("With simple keys, key nodes cannot have comments");if(u instanceof a.default){throw new Error("With simple keys, collection cannot be used as a key value")}}var c=!s&&(!u||l||u instanceof a.default||u.type===T.Type.BLOCK_FOLDED||u.type===T.Type.BLOCK_LITERAL),d=e,h=d.doc,p=d.indent;e=Object.assign({},e,{implicitKey:!c,indent:p+"  "});var v=!1,g=h.schema.stringify(u,e,(function(){return l=null}),(function(){return v=!0}));if(g=(0,n.default)(g,e.indent,l),e.allNullValues&&!s)return this.comment?(g=(0,n.default)(g,e.indent,this.comment),t&&t()):v&&!l&&r&&r(),e.inFlow?g:"? ".concat(g);g=c?"? ".concat(g,"\n").concat(p,":"):"".concat(g,":"),this.comment&&(g=(0,n.default)(g,e.indent,this.comment),t&&t());var m="",y=null;if(f instanceof o.default){if(f.spaceBefore&&(m="\n"),f.commentBefore){var _=f.commentBefore.replace(/^/gm,"".concat(e.indent,"#"));m+="\n".concat(_)}y=f.comment}else f&&"object"===i(f)&&(f=h.schema.createNode(f,!0));e.implicitKey=!1,v=!1;var b=h.schema.stringify(f,e,(function(){return y=null}),(function(){return v=!0})),w=" ";if(m||this.comment)w="".concat(m,"\n").concat(e.indent);else if(!c&&f instanceof a.default){("["===b[0]||"{"===b[0])&&!b.includes("\n")||(w="\n".concat(e.indent))}return v&&!y&&r&&r(),(0,n.default)(g+w+b,e.indent,y)}},{key:"commentBefore",get:function(){return this.key&&this.key.commentBefore},set:function(e){null==this.key&&(this.key=new u.default(null)),this.key.commentBefore=e}}]),t}(o.default);t.default=h}));a(Z);var X=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.isEmptyPath=void 0;var n=u(J),r=u(H),a=u(Z),o=u(z);function u(e){return e&&e.__esModule?e:{default:e}}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var h=function(e){return null==e||"object"===i(e)&&e[Symbol.iterator]().next().done};t.isEmptyPath=h;var p=function(e){function t(){var e,n;s(this,t);for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return d(v(n=g(this,(e=c(t)).call.apply(e,[this].concat(a)))),"items",[]),n}return l(t,e),f(t,[{key:"addIn",value:function(e,n){if(h(e))this.add(n);else{var r=_(e),a=r[0],o=r.slice(1),i=this.get(a,!0);if(!(i instanceof t))throw new Error("Expected YAML collection at ".concat(a,". Remaining path: ").concat(o));i.addIn(o,n)}}},{key:"deleteIn",value:function(e){var n=_(e),r=n[0],a=n.slice(1);if(0===a.length)return this.delete(r);var o=this.get(r,!0);if(o instanceof t)return o.deleteIn(a);throw new Error("Expected YAML collection at ".concat(r,". Remaining path: ").concat(a))}},{key:"getIn",value:function(e,n){var r=_(e),a=r[0],i=r.slice(1),s=this.get(a,!0);return 0===i.length?!n&&s instanceof o.default?s.value:s:s instanceof t?s.getIn(i,n):void 0}},{key:"hasAllNullValues",value:function(){return this.items.every((function(e){if(!(e instanceof a.default))return!1;var t=e.value;return null==t||t instanceof o.default&&null==t.value&&!t.commentBefore&&!t.comment&&!t.tag}))}},{key:"hasIn",value:function(e){var n=_(e),r=n[0],a=n.slice(1);if(0===a.length)return this.has(r);var o=this.get(r,!0);return o instanceof t&&o.hasIn(a)}},{key:"setIn",value:function(e,n){var r=_(e),a=r[0],o=r.slice(1);if(0===o.length)this.set(a,n);else{var i=this.get(a,!0);if(!(i instanceof t))throw new Error("Expected YAML collection at ".concat(a,". Remaining path: ").concat(o));i.setIn(o,n)}}},{key:"toJSON",value:function(){return null}},{key:"toString",value:function(e,r,a,o){var i=this,s=r.blockItem,u=r.flowChars,f=r.isMap,l=r.itemIndent,c=e,d=c.doc,h=c.indent,p=this.type&&"FLOW"===this.type.substr(0,4)||e.inFlow;p&&(l+="  ");var v=f&&this.hasAllNullValues();e=Object.assign({},e,{allNullValues:v,indent:l,inFlow:p,type:null});var g,m=!1,y=!1,_=this.items.reduce((function(t,r,a){var o;r&&(!m&&r.spaceBefore&&t.push({type:"comment",str:""}),r.commentBefore&&r.commentBefore.match(/^.*$/gm).forEach((function(e){t.push({type:"comment",str:"#".concat(e)})})),r.comment&&(o=r.comment),p&&(!m&&r.spaceBefore||r.commentBefore||r.comment||r.key&&(r.key.commentBefore||r.key.comment)||r.value&&(r.value.commentBefore||r.value.comment))&&(y=!0)),m=!1;var s=d.schema.stringify(r,e,(function(){return o=null}),(function(){return m=!0}));return p&&!y&&s.includes("\n")&&(y=!0),p&&a<i.items.length-1&&(s+=","),s=(0,n.default)(s,l,o),m&&(o||p)&&(m=!1),t.push({type:"item",str:s}),t}),[]);if(0===_.length)g=u.start+u.end;else if(p){var b=u.start,w=u.end,E=_.map((function(e){return e.str}));if(y||E.reduce((function(e,t){return e+t.length+2}),2)>t.maxFlowStringSingleLineLength){g=b;var O=!0,M=!1,S=void 0;try{for(var A,k=E[Symbol.iterator]();!(O=(A=k.next()).done);O=!0){var L=A.value;g+=L?"\n  ".concat(h).concat(L):"\n"}}catch(e){M=!0,S=e}finally{try{O||null==k.return||k.return()}finally{if(M)throw S}}g+="\n".concat(h).concat(w)}else g="".concat(b," ").concat(E.join(" ")," ").concat(w)}else{var T=_.map(s);g=T.shift();var P=!0,C=!1,N=void 0;try{for(var x,R=T[Symbol.iterator]();!(P=(x=R.next()).done);P=!0){var I=x.value;g+=I?"\n".concat(h).concat(I):"\n"}}catch(e){C=!0,N=e}finally{try{P||null==R.return||R.return()}finally{if(C)throw N}}}return this.comment?(g+="\n"+this.comment.replace(/^/gm,"".concat(h,"#")),a&&a()):m&&o&&o(),g}}]),t}(r.default);t.default=p,d(p,"maxFlowStringSingleLineLength",60)}));a(X);X.isEmptyPath;var ee=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(G),r=i(X),a=i(H),o=i(Z);function i(e){return e&&e.__esModule?e:{default:e}}var u,d,h,p=function e(t,n){if(t instanceof v){var a=n.find((function(e){return e.node===t.source}));return a.count*a.aliasCount}if(t instanceof r.default){var i=0,s=!0,u=!1,f=void 0;try{for(var l,c=t.items[Symbol.iterator]();!(s=(l=c.next()).done);s=!0){var d=e(l.value,n);d>i&&(i=d)}}catch(e){u=!0,f=e}finally{try{s||null==c.return||c.return()}finally{if(u)throw f}}return i}if(t instanceof o.default){var h=e(t.key,n),p=e(t.value,n);return Math.max(h,p)}return 1},v=function(e){function t(e){var n;return s(this,t),(n=g(this,c(t).call(this))).source=e,n.type=T.Type.ALIAS,n}return l(t,e),f(t,null,[{key:"stringify",value:function(e,t){var n=e.range,r=e.source,a=t.anchors,o=t.doc,i=t.implicitKey,s=t.inStringifyKey,u=Object.keys(a).find((function(e){return a[e]===r}));if(!u&&s&&(u=o.anchors.getName(r)||o.anchors.newName()),u)return"*".concat(u).concat(i?" ":"");var f=o.anchors.getName(r)?"Alias node must be after source node":"Source node not found for alias node";throw new Error("".concat(f," [").concat(n,"]"))}}]),f(t,[{key:"toJSON",value:function(e,t){var r=this;if(!t)return(0,n.default)(this.source,e,t);var a=t.anchors,o=t.maxAliasCount,i=a.find((function(e){return e.node===r.source}));if(!i||void 0===i.res){var s="This should not happen: Alias anchor was not resolved?";throw this.cstNode?new x.YAMLReferenceError(this.cstNode,s):new ReferenceError(s)}if(o>=0&&(i.count+=1,0===i.aliasCount&&(i.aliasCount=p(this.source,a)),i.count*i.aliasCount>o)){var u="Excessive alias count indicates a resource exhaustion attack";throw this.cstNode?new x.YAMLReferenceError(this.cstNode,u):new ReferenceError(u)}return i.res}},{key:"toString",value:function(e){return t.stringify(this,e)}},{key:"tag",set:function(e){throw new Error("Alias nodes cannot have tags")}}]),t}(a.default);t.default=v,h=!0,(d="default")in(u=v)?Object.defineProperty(u,d,{value:h,enumerable:!0,configurable:!0,writable:!0}):u[d]=h}));a(ee);var te=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.findPair=i,t.default=void 0;var n=o(X),r=o(Z),a=o(z);function o(e){return e&&e.__esModule?e:{default:e}}function i(e,t){var n=t instanceof a.default?t.value:t,o=!0,i=!1,s=void 0;try{for(var u,f=e[Symbol.iterator]();!(o=(u=f.next()).done);o=!0){var l=u.value;if(l instanceof r.default){if(l.key===t||l.key===n)return l;if(l.key&&l.key.value===n)return l}}}catch(e){i=!0,s=e}finally{try{o||null==f.return||f.return()}finally{if(i)throw s}}}var u=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"add",value:function(e){if(e?e instanceof r.default||(e=new r.default(e.key||e,e.value)):e=new r.default(e),i(this.items,e.key))throw new Error("Key ".concat(e.key," already set"));this.items.push(e)}},{key:"delete",value:function(e){var t=i(this.items,e);return!!t&&this.items.splice(this.items.indexOf(t),1).length>0}},{key:"get",value:function(e,t){var n=i(this.items,e),r=n&&n.value;return!t&&r instanceof a.default?r.value:r}},{key:"has",value:function(e){return!!i(this.items,e)}},{key:"set",value:function(e,t){var n=i(this.items,e);n?n.value=t:this.items.push(new r.default(e,t))}},{key:"toJSON",value:function(e,t,n){var r=n?new n:t&&t.mapAsMap?new Map:{};t&&t.onCreate&&t.onCreate(r);var a=!0,o=!1,i=void 0;try{for(var s,u=this.items[Symbol.iterator]();!(a=(s=u.next()).done);a=!0){s.value.addToJSMap(t,r)}}catch(e){o=!0,i=e}finally{try{a||null==u.return||u.return()}finally{if(o)throw i}}return r}},{key:"toString",value:function(e,n,a){if(!e)return JSON.stringify(this);var o=!0,i=!1,s=void 0;try{for(var u,f=this.items[Symbol.iterator]();!(o=(u=f.next()).done);o=!0){var l=u.value;if(!(l instanceof r.default))throw new Error("Map items must all be pairs; found ".concat(JSON.stringify(l)," instead"))}}catch(e){i=!0,s=e}finally{try{o||null==f.return||f.return()}finally{if(i)throw s}}return m(c(t.prototype),"toString",this).call(this,e,{blockItem:function(e){return e.str},flowChars:{start:"{",end:"}"},isMap:!0,itemIndent:e.indent||""},n,a)}}]),t}(n.default);t.default=u}));a(te);te.findPair;var ne=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(G),r=o(X),a=o(z);function o(e){return e&&e.__esModule?e:{default:e}}function i(e){var t=e instanceof a.default?e.value:e;return t&&"string"==typeof t&&(t=Number(t)),Number.isInteger(t)&&t>=0?t:null}var u=function(e){function t(){return s(this,t),g(this,c(t).apply(this,arguments))}return l(t,e),f(t,[{key:"add",value:function(e){this.items.push(e)}},{key:"delete",value:function(e){var t=i(e);return"number"==typeof t&&this.items.splice(t,1).length>0}},{key:"get",value:function(e,t){var n=i(e);if("number"==typeof n){var r=this.items[n];return!t&&r instanceof a.default?r.value:r}}},{key:"has",value:function(e){var t=i(e);return"number"==typeof t&&t<this.items.length}},{key:"set",value:function(e,t){var n=i(e);if("number"!=typeof n)throw new Error("Expected a valid index, not ".concat(e,"."));this.items[n]=t}},{key:"toJSON",value:function(e,t){var r=[];t&&t.onCreate&&t.onCreate(r);var a=0,o=!0,i=!1,s=void 0;try{for(var u,f=this.items[Symbol.iterator]();!(o=(u=f.next()).done);o=!0){var l=u.value;r.push((0,n.default)(l,String(a++),t))}}catch(e){i=!0,s=e}finally{try{o||null==f.return||f.return()}finally{if(i)throw s}}return r}},{key:"toString",value:function(e,n,r){return e?m(c(t.prototype),"toString",this).call(this,e,{blockItem:function(e){return"comment"===e.type?e.str:"- ".concat(e.str)},flowChars:{start:"[",end:"]"},isMap:!1,itemIndent:(e.indent||"")+"  "},n,r):JSON.stringify(this)}}]),t}(r.default);t.default=u}));a(ne);var re=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.MERGE_KEY=void 0;var n=i(te),r=i(Z),a=i(z),o=i(ne);function i(e){return e&&e.__esModule?e:{default:e}}var u="<<";t.MERGE_KEY=u;var d=function(e){function t(e){var n;if(s(this,t),e instanceof r.default){var i=e.value;i instanceof o.default||((i=new o.default).items.push(e.value),i.range=e.value.range),(n=g(this,c(t).call(this,e.key,i))).range=e.range}else n=g(this,c(t).call(this,new a.default(u),new o.default));return n.type="MERGE_PAIR",g(n)}return l(t,e),f(t,[{key:"addToJSMap",value:function(e,t){var r=!0,a=!1,o=void 0;try{for(var i,s=this.value.items[Symbol.iterator]();!(r=(i=s.next()).done);r=!0){var u=i.value.source;if(!(u instanceof n.default))throw new Error("Merge sources must be maps");var f=u.toJSON(null,e,Map),l=!0,c=!1,d=void 0;try{for(var h,p=f[Symbol.iterator]();!(l=(h=p.next()).done);l=!0){var v=y(h.value,2),g=v[0],m=v[1];t instanceof Map?t.has(g)||t.set(g,m):t instanceof Set?t.add(g):Object.prototype.hasOwnProperty.call(t,g)||(t[g]=m)}}catch(e){c=!0,d=e}finally{try{l||null==p.return||p.return()}finally{if(c)throw d}}}}catch(e){a=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(a)throw o}}return t}},{key:"toString",value:function(e,n){var r=this.value;if(r.items.length>1)return m(c(t.prototype),"toString",this).call(this,e,n);this.value=r.items[0];var a=m(c(t.prototype),"toString",this).call(this,e,n);return this.value=r,a}}]),t}(r.default);t.default=d}));a(re);re.MERGE_KEY;var ae=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=u(ee),r=u(te),a=u(re),o=u(z),i=u(ne);function u(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(t){var n,r,a;s(this,e),a={},(r="map")in(n=this)?Object.defineProperty(n,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[r]=a,this.prefix=t}return f(e,null,[{key:"validAnchorNode",value:function(e){return e instanceof o.default||e instanceof i.default||e instanceof r.default}}]),f(e,[{key:"createAlias",value:function(e,t){return this.setAnchor(e,t),new n.default(e)}},{key:"createMergePair",value:function(){for(var e=this,t=new a.default,o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return t.value.items=i.map((function(t){if(t instanceof n.default){if(t.source instanceof r.default)return t}else if(t instanceof r.default)return e.createAlias(t);throw new Error("Merge sources must be Map nodes or their Aliases")})),t}},{key:"getName",value:function(e){var t=this.map;return Object.keys(t).find((function(n){return t[n]===e}))}},{key:"getNode",value:function(e){return this.map[e]}},{key:"newName",value:function(e){e||(e=this.prefix);for(var t=Object.keys(this.map),n=1;;++n){var r="".concat(e).concat(n);if(-1===t.indexOf(r))return r}}},{key:"resolveNodes",value:function(){var e=this.map,t=this._cstAliases;Object.keys(e).forEach((function(t){e[t]=e[t].resolved})),t.forEach((function(e){e.source=e.source.resolved})),delete this._cstAliases}},{key:"setAnchor",value:function(t,n){if(null!=t&&!e.validAnchorNode(t))throw new Error("Anchors may only be set for Scalar, Seq and Map nodes");if(n&&/[\x00-\x19\s,[\]{}]/.test(n))throw new Error("Anchor names must not contain whitespace or control characters");var r=this.map,a=t&&Object.keys(r).find((function(e){return r[e]===t}));if(a){if(!n)return a;a!==n&&(delete r[a],r[n]=t)}else{if(!n){if(!t)return null;n=this.newName()}r[n]=t}return n}}]),e}();t.default=l}));a(ae);var oe=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(X),r=o(Z),a=o(z);function o(e){return e&&e.__esModule?e:{default:e}}t.default=function(e){return Object.keys(function e(t,o){if(t&&"object"===i(t)){var s=t.tag;t instanceof n.default?(s&&(o[s]=!0),t.items.forEach((function(t){return e(t,o)}))):t instanceof r.default?(e(t.key,o),e(t.value,o)):t instanceof a.default&&s&&(o[s]=!0)}return o}(e,{}))}}));a(oe);var ie=o((function(e,t){function n(e,t){if(!r||!r._YAML_SILENCE_WARNINGS){var n=(r&&r.process).emitWarning;n?n(e,t):console.warn(t?"".concat(t,": ").concat(e):e)}}Object.defineProperty(t,"__esModule",{value:!0}),t.warn=n,t.warnFileDeprecation=function(e){if(r&&r._YAML_SILENCE_DEPRECATION_WARNINGS)return;var t=e.replace(/.*yaml[/\\]/i,"").replace(/\.js$/,"").replace(/\\/g,"/");n("The endpoint 'yaml/".concat(t,"' will be removed in a future release."),"DeprecationWarning")},t.warnOptionDeprecation=function(e,t){if(r&&r._YAML_SILENCE_DEPRECATION_WARNINGS)return;if(a[e])return;a[e]=!0;var o="The option '".concat(e,"' will be removed in a future release");n(o+=t?", use '".concat(t,"' instead."):".","DeprecationWarning")};var a={}}));a(ie);ie.warn,ie.warnFileDeprecation,ie.warnOptionDeprecation;var se=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,o,i){var s=i.indentAtStart,u=i.lineWidth,f=void 0===u?80:u,l=i.minContentWidth,c=void 0===l?20:l,d=i.onFold,h=i.onOverflow;if(!f||f<0)return e;var p=Math.max(1+c,1+f-t.length);if(e.length<=p)return e;var v,g=[],m={},y=f-("number"==typeof s?s:t.length),_=void 0,b=void 0,w=!1,E=-1;o===n&&-1!==(E=a(e,E))&&(y=E+p);for(;v=e[E+=1];){if(o===r&&"\\"===v)switch(e[E+1]){case"x":E+=3;break;case"u":E+=5;break;case"U":E+=9;break;default:E+=1}if("\n"===v)o===n&&(E=a(e,E)),y=E+p,_=void 0;else{if(" "===v&&b&&" "!==b&&"\n"!==b&&"\t"!==b){var O=e[E+1];O&&" "!==O&&"\n"!==O&&"\t"!==O&&(_=E)}if(E>=y)if(_)g.push(_),y=_+p,_=void 0;else if(o===r){for(;" "===b||"\t"===b;)b=v,v=e[E+=1],w=!0;g.push(E-2),m[E-2]=!0,y=E-2+p,_=void 0}else w=!0}b=v}w&&h&&h();if(0===g.length)return e;d&&d();for(var M=e.slice(0,g[0]),S=0;S<g.length;++S){var A=g[S],k=g[S+1]||e.length;o===r&&m[A]&&(M+="".concat(e[A],"\\")),M+="\n".concat(t).concat(e.slice(A+1,k))}return M},t.FOLD_QUOTED=t.FOLD_BLOCK=t.FOLD_FLOW=void 0;t.FOLD_FLOW="flow";var n="block";t.FOLD_BLOCK=n;var r="quoted";t.FOLD_QUOTED=r;var a=function(e,t){for(var n=e[t+1];" "===n||"\t"===n;){do{n=e[t+=1]}while(n&&"\n"!==n);n=e[t+1]}return t}}));a(se);se.FOLD_QUOTED,se.FOLD_BLOCK,se.FOLD_FLOW;var ue=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.strOptions=t.nullOptions=t.boolOptions=t.binaryOptions=void 0;var n={defaultType:T.Type.BLOCK_LITERAL,lineWidth:76};t.binaryOptions=n;t.boolOptions={trueStr:"true",falseStr:"false"};t.nullOptions={nullStr:"null"};var r={defaultType:T.Type.PLAIN,doubleQuoted:{jsonEncoding:!1,minMultiLineLength:40},fold:{lineWidth:80,minContentWidth:20}};t.strOptions=r}));a(ue);ue.strOptions,ue.nullOptions,ue.boolOptions,ue.binaryOptions;var fe=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.stringifyNumber=function(e){var t=e.format,n=e.minFractionDigits,r=e.tag,a=e.value;if(!isFinite(a))return isNaN(a)?".nan":a<0?"-.inf":".inf";var o=JSON.stringify(a);if(!t&&n&&(!r||"tag:yaml.org,2002:float"===r)&&/^\d/.test(o)){var i=o.indexOf(".");i<0&&(i=o.length,o+=".");for(var s=n-(o.length-i-1);s-- >0;)o+="0"}return o},t.stringifyString=function(e,t,i,s){var u=ue.strOptions.defaultType,f=t.implicitKey,l=t.inFlow,c=e,d=c.type,h=c.value;"string"!=typeof h&&(h=String(h),e=Object.assign({},e,{value:h}));var p=function(u){switch(u){case T.Type.BLOCK_FOLDED:case T.Type.BLOCK_LITERAL:return o(e,t,i,s);case T.Type.QUOTE_DOUBLE:return r(h,t);case T.Type.QUOTE_SINGLE:return a(h,t);case T.Type.PLAIN:return function(e,t,i,s){var u=e.comment,f=e.type,l=e.value,c=t.actualString,d=t.implicitKey,h=t.indent,p=t.inFlow,v=t.tags;if(d&&/[\n[\]{},]/.test(l)||p&&/[[\]{},]/.test(l))return r(l,t);if(!l||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(l))return d||p||-1===l.indexOf("\n")?-1!==l.indexOf('"')&&-1===l.indexOf("'")?a(l,t):r(l,t):o(e,t,i,s);if(!d&&!p&&f!==T.Type.PLAIN&&-1!==l.indexOf("\n"))return o(e,t,i,s);var g=l.replace(/\n+/g,"$&\n".concat(h));if(c&&"string"!=typeof v.resolveScalar(g).value)return r(l,t);var m=d?g:(0,n.default)(g,h,n.FOLD_FLOW,ue.strOptions.fold);if(u&&!p&&(-1!==m.indexOf("\n")||-1!==u.indexOf("\n")))return i&&i(),(0,J.addCommentBefore)(m,h,u);return m}(e,t,i,s);default:return null}};d!==T.Type.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f]/.test(h)?d=T.Type.QUOTE_DOUBLE:!f&&!l||d!==T.Type.BLOCK_FOLDED&&d!==T.Type.BLOCK_LITERAL||(d=T.Type.QUOTE_DOUBLE);var v=p(d);if(null===v&&null===(v=p(u)))throw new Error("Unsupported default string type ".concat(u));return v};var n=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}return t.default=e,t}(se);function r(e,t){var r=t.implicitKey,a=t.indent,o=ue.strOptions.doubleQuoted,i=o.jsonEncoding,s=o.minMultiLineLength,u=JSON.stringify(e);if(i)return u;for(var f="",l=0,c=0,d=u[c];d;d=u[++c])if(" "===d&&"\\"===u[c+1]&&"n"===u[c+2]&&(f+=u.slice(l,c)+"\\ ",l=c+=1,d="\\"),"\\"===d)switch(u[c+1]){case"u":f+=u.slice(l,c);var h=u.substr(c+2,4);switch(h){case"0000":f+="\\0";break;case"0007":f+="\\a";break;case"000b":f+="\\v";break;case"001b":f+="\\e";break;case"0085":f+="\\N";break;case"00a0":f+="\\_";break;case"2028":f+="\\L";break;case"2029":f+="\\P";break;default:"00"===h.substr(0,2)?f+="\\x"+h.substr(2):f+=u.substr(c,6)}l=(c+=5)+1;break;case"n":if(r||'"'===u[c+2]||u.length<s)c+=1;else{for(f+=u.slice(l,c)+"\n\n";"\\"===u[c+2]&&"n"===u[c+3]&&'"'!==u[c+4];)f+="\n",c+=2;f+=a," "===u[c+2]&&(f+="\\"),l=(c+=1)+1}break;default:c+=1}return f=l?f+u.slice(l):u,r?f:(0,n.default)(f,a,n.FOLD_QUOTED,ue.strOptions.fold)}function a(e,t){var a=t.indent,o=t.implicitKey;if(o){if(/\n/.test(e))return r(e,t)}else if(/[ \t]\n|\n[ \t]/.test(e))return r(e,t);var i="'"+e.replace(/'/g,"''").replace(/\n+/g,"$&\n".concat(a))+"'";return o?i:(0,n.default)(i,a,n.FOLD_FLOW,ue.strOptions.fold)}function o(e,t,a,o){var i=e.comment,s=e.type,u=e.value;if(/\n[\t ]+$/.test(u)||/^\s*$/.test(u))return r(u,t);var f=t.indent||(t.forceBlockIndent?" ":""),l=f?"2":"1",c=s!==T.Type.BLOCK_FOLDED&&(s===T.Type.BLOCK_LITERAL||!function(e,t){var n=e.length;if(n<=t)return!1;for(var r=0,a=0;r<n;++r)if("\n"===e[r]){if(r-a>t)return!0;if(n-(a=r+1)<=t)return!1}return!0}(u,ue.strOptions.fold.lineWidth-f.length)),d=c?"|":">";if(!u)return d+"\n";var h="",p="";if(u=u.replace(/[\n\t ]*$/,(function(e){var t=e.indexOf("\n");return-1===t?d+="-":u!==e&&t===e.length-1||(d+="+",o&&o()),p=e.replace(/\n$/,""),""})).replace(/^[\n ]*/,(function(e){-1!==e.indexOf(" ")&&(d+=l);var t=e.match(/ +$/);return t?(h=e.slice(0,-t[0].length),t[0]):(h=e,"")})),p&&(p=p.replace(/\n+(?!\n|$)/g,"$&".concat(f))),h&&(h=h.replace(/\n+/g,"$&".concat(f))),i&&(d+=" #"+i.replace(/ ?[\r\n]+/g," "),a&&a()),!u)return"".concat(d).concat(l,"\n").concat(f).concat(p);if(c)return u=u.replace(/\n+/g,"$&".concat(f)),"".concat(d,"\n").concat(f).concat(h).concat(u).concat(p);u=u.replace(/\n+/g,"\n$&").replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,"$&".concat(f));var v=(0,n.default)("".concat(h).concat(u).concat(p),f,n.FOLD_BLOCK,ue.strOptions.fold);return"".concat(d,"\n").concat(f).concat(v)}}));a(fe);fe.stringifyNumber,fe.stringifyString;var le=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.checkFlowCollectionEnd=function(e,t){var n,r,a;switch(t.type){case T.Type.FLOW_MAP:n="}",r="flow map";break;case T.Type.FLOW_SEQ:n="]",r="flow sequence";break;default:return void e.push(new x.YAMLSemanticError(t,"Not a flow collection!?"))}for(var o=t.items.length-1;o>=0;--o){var i=t.items[o];if(!i||i.type!==T.Type.COMMENT){a=i;break}}if(a&&a.char!==n){var s,u="Expected ".concat(r," to end with ").concat(n);"number"==typeof a.offset?(s=new x.YAMLSemanticError(t,u)).offset=a.offset+1:(s=new x.YAMLSemanticError(a,u),a.range&&a.range.end&&(s.offset=a.range.end-a.range.start)),e.push(s)}},t.checkKeyLength=function(e,t,n,r,a){if(!r||"number"!=typeof a)return;var o=t.items[n],i=o&&o.range&&o.range.start;if(!i)for(var s=n-1;s>=0;--s){var u=t.items[s];if(u&&u.range){i=u.range.end+2*(n-s);break}}if(i>a+1024){var f=String(r).substr(0,8)+"..."+String(r).substr(-8);e.push(new x.YAMLSemanticError(t,'The "'.concat(f,'" key is too long')))}},t.resolveComments=function(e,t){var n=!0,r=!1,a=void 0;try{for(var o,i=t[Symbol.iterator]();!(n=(o=i.next()).done);n=!0){var s=o.value,u=s.afterKey,f=s.before,l=s.comment,c=e.items[f];c?(u&&c.value&&(c=c.value),void 0===l?!u&&c.commentBefore||(c.spaceBefore=!0):c.commentBefore?c.commentBefore+="\n"+l:c.commentBefore=l):void 0!==l&&(e.comment?e.comment+="\n"+l:e.comment=l)}}catch(e){r=!0,a=e}finally{try{n||null==i.return||i.return()}finally{if(r)throw a}}}}));a(le);le.checkFlowCollectionEnd,le.checkKeyLength,le.resolveComments;var ce=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.type!==T.Type.MAP&&t.type!==T.Type.FLOW_MAP){var u="A ".concat(t.type," node cannot be resolved as a mapping");return e.errors.push(new x.YAMLSyntaxError(t,u)),null}var f=t.type===T.Type.FLOW_MAP?function(e,t){for(var n=[],r=[],a=void 0,i=null,s=!1,u="{",f=0;f<t.items.length;++f){(0,le.checkKeyLength)(e.errors,t,f,a,i);var l=t.items[f];if("string"==typeof l.char){var c=l.char,d=l.offset;if("?"===c&&void 0===a&&!s){s=!0,u=":";continue}if(":"===c){if(void 0===a&&(a=null),":"===u){u=",";continue}}else if(s&&(void 0===a&&","!==c&&(a=null),s=!1),void 0!==a&&(r.push(new o.default(a)),a=void 0,i=null,","===c)){u=":";continue}if("}"===c){if(f===t.items.length-1)continue}else if(c===u){u=":";continue}var h="Flow map contains an unexpected ".concat(c),p=new x.YAMLSyntaxError(t,h);p.offset=d,e.errors.push(p)}else l.type===T.Type.BLANK_LINE?n.push({afterKey:!!a,before:r.length}):l.type===T.Type.COMMENT?n.push({afterKey:!!a,before:r.length,comment:l.comment}):void 0===a?(","===u&&e.errors.push(new x.YAMLSemanticError(l,"Separator , missing in flow map")),a=e.resolveNode(l),i=s?null:l.range.start):(","!==u&&e.errors.push(new x.YAMLSemanticError(l,"Indicator : missing in flow map entry")),r.push(new o.default(a,e.resolveNode(l))),a=void 0,s=!1)}(0,le.checkFlowCollectionEnd)(e.errors,t),void 0!==a&&r.push(new o.default(a));return{comments:n,items:r}}(e,t):function(e,t){for(var r=[],a=[],i=void 0,s=null,u=0;u<t.items.length;++u){var f=t.items[u];switch(f.type){case T.Type.BLANK_LINE:r.push({afterKey:!!i,before:a.length});break;case T.Type.COMMENT:r.push({afterKey:!!i,before:a.length,comment:f.comment});break;case T.Type.MAP_KEY:void 0!==i&&a.push(new o.default(i)),f.error&&e.errors.push(f.error),i=e.resolveNode(f.node),s=null;break;case T.Type.MAP_VALUE:if(void 0===i&&(i=null),f.error&&e.errors.push(f.error),!f.context.atLineStart&&f.node&&f.node.type===T.Type.MAP&&!f.node.context.atLineStart){e.errors.push(new x.YAMLSemanticError(f.node,"Nested mappings are not allowed in compact mappings"))}var c=f.node;if(!c&&f.props.length>0){(c=new n.default(T.Type.PLAIN,[])).context={parent:f,src:f.context.src};var d=f.range.start+1;if(c.range={start:d,end:d},c.valueRange={start:d,end:d},"number"==typeof f.range.origStart){var h=f.range.origStart+1;c.range.origStart=c.range.origEnd=h,c.valueRange.origStart=c.valueRange.origEnd=h}}var p=new o.default(i,e.resolveNode(c));l(f,p),a.push(p),(0,le.checkKeyLength)(e.errors,t,u,i,s),i=void 0,s=null;break;default:void 0!==i&&a.push(new o.default(i)),i=e.resolveNode(f),s=f.range.start,f.error&&e.errors.push(f.error);e:for(var v=u+1;;++v){var g=t.items[v];switch(g&&g.type){case T.Type.BLANK_LINE:case T.Type.COMMENT:continue e;case T.Type.MAP_VALUE:break e;default:e.errors.push(new x.YAMLSemanticError(f,"Implicit map keys need to be followed by map values"));break e}}if(f.valueRangeContainsNewline){e.errors.push(new x.YAMLSemanticError(f,"Implicit map keys need to be on a single line"))}}}void 0!==i&&a.push(new o.default(i));return{comments:r,items:a}}(e,t),c=f.comments,d=f.items,h=new r.default;h.items=d,(0,le.resolveComments)(h,c);for(var p=!1,v=0;v<d.length;++v){var g=d[v].key;if(g instanceof s.default&&(p=!0),e.schema.merge&&g&&g.value===a.MERGE_KEY){d[v]=new a.default(d[v]);var m=d[v].value.items,y=null;m.some((function(e){if(e instanceof i.default){var t=e.source.type;return t!==T.Type.MAP&&t!==T.Type.FLOW_MAP&&(y="Merge nodes aliases can only point to maps")}return y="Merge nodes can only have Alias nodes as values"})),y&&e.errors.push(new x.YAMLSemanticError(t,y))}else for(var _=v+1;_<d.length;++_){var b=d[_].key;if(g===b||g&&b&&Object.prototype.hasOwnProperty.call(g,"value")&&g.value===b.value){var w='Map keys must be unique; "'.concat(g,'" is repeated');e.errors.push(new x.YAMLSemanticError(t,w));break}}}if(p&&!e.options.mapAsMap){e.warnings.push(new x.YAMLWarning(t,"Keys with collection values will be stringified as YAML due to JS Object restrictions. Use mapAsMap: true to avoid this."))}return t.resolved=h,h};var n=u(W),r=u(te),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}return t.default=e,t}(re),o=u(Z),i=u(ee),s=u(X);function u(e){return e&&e.__esModule?e:{default:e}}var f=function(e){var t=e.context,n=t.lineStart,r=t.node,a=t.src,o=e.props;if(0===o.length)return!1;var i=o[0].start;if(r&&i>r.valueRange.start)return!1;if(a[i]!==T.Char.COMMENT)return!1;for(var s=n;s<i;++s)if("\n"===a[s])return!1;return!0};function l(e,t){if(f(e)){var n=e.getPropValue(0,T.Char.COMMENT,!0),r=!1,a=t.value.commentBefore;if(a&&a.startsWith(n))t.value.commentBefore=a.substr(n.length+1),r=!0;else{var o=t.value.comment;!e.node&&o&&o.startsWith(n)&&(t.value.comment=o.substr(n.length+1),r=!0)}r&&(t.comment=n)}}}));a(ce);var de=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(te),r=a(ce);function a(e){return e&&e.__esModule?e:{default:e}}var o={createNode:function(e,t,r){var a=new n.default;if(t instanceof Map){var o=!0,s=!1,u=void 0;try{for(var f,l=t[Symbol.iterator]();!(o=(f=l.next()).done);o=!0){var c=y(f.value,2),d=c[0],h=c[1];a.items.push(e.createPair(d,h,r))}}catch(e){s=!0,u=e}finally{try{o||null==l.return||l.return()}finally{if(s)throw u}}}else if(t&&"object"===i(t))for(var p=0,v=Object.keys(t);p<v.length;p++){var g=v[p];a.items.push(e.createPair(g,t[g],r))}return a},default:!0,nodeClass:n.default,tag:"tag:yaml.org,2002:map",resolve:r.default};t.default=o}));a(de);var he=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.type!==T.Type.SEQ&&t.type!==T.Type.FLOW_SEQ){var o="A ".concat(t.type," node cannot be resolved as a sequence");return e.errors.push(new x.YAMLSyntaxError(t,o)),null}var i=t.type===T.Type.FLOW_SEQ?function(e,t){for(var r=[],a=[],o=!1,i=void 0,s=null,u="[",f=0;f<t.items.length;++f){var l=t.items[f];if("string"==typeof l.char){var c=l.char,d=l.offset;if(":"===c||!o&&void 0===i||(o&&void 0===i&&(i=u?a.pop():null),a.push(new n.default(i)),o=!1,i=void 0,s=null),c===u)u=null;else if(u||"?"!==c){if("["!==u&&":"===c&&void 0===i){if(","===u){if((i=a.pop())instanceof n.default){var h=new x.YAMLSemanticError(t,"Chaining flow sequence pairs is invalid");h.offset=d,e.errors.push(h)}o||(0,le.checkKeyLength)(e.errors,t,f,i,s)}else i=null;s=null,o=!1,u=null}else if("["===u||"]"!==c||f<t.items.length-1){var p="Flow sequence contains an unexpected ".concat(c),v=new x.YAMLSyntaxError(t,p);v.offset=d,e.errors.push(v)}}else o=!0}else if(l.type===T.Type.BLANK_LINE)r.push({before:a.length});else if(l.type===T.Type.COMMENT)r.push({comment:l.comment,before:a.length});else{if(u){var g="Expected a ".concat(u," in flow sequence");e.errors.push(new x.YAMLSemanticError(l,g))}var m=e.resolveNode(l);void 0===i?a.push(m):(a.push(new n.default(i,m)),i=void 0),s=l.range.start,u=","}}(0,le.checkFlowCollectionEnd)(e.errors,t),void 0!==i&&a.push(new n.default(i));return{comments:r,items:a}}(e,t):function(e,t){for(var n=[],r=[],a=0;a<t.items.length;++a){var o=t.items[a];switch(o.type){case T.Type.BLANK_LINE:n.push({before:r.length});break;case T.Type.COMMENT:n.push({comment:o.comment,before:r.length});break;case T.Type.SEQ_ITEM:if(o.error&&e.errors.push(o.error),r.push(e.resolveNode(o.node)),o.hasProps){e.errors.push(new x.YAMLSemanticError(o,"Sequence items cannot have tags or anchors before the - indicator"))}break;default:o.error&&e.errors.push(o.error),e.errors.push(new x.YAMLSyntaxError(o,"Unexpected ".concat(o.type," node in sequence")))}}return{comments:n,items:r}}(e,t),s=i.comments,u=i.items,f=new r.default;if(f.items=u,(0,le.resolveComments)(f,s),!e.options.mapAsMap&&u.some((function(e){return e instanceof n.default&&e.key instanceof a.default}))){e.warnings.push(new x.YAMLWarning(t,"Keys with collection values will be stringified as YAML due to JS Object restrictions. Use mapAsMap: true to avoid this."))}return t.resolved=f,f};var n=o(Z),r=o(ne),a=o(X);function o(e){return e&&e.__esModule?e:{default:e}}}));a(he);var pe=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(he),r=a(ne);function a(e){return e&&e.__esModule?e:{default:e}}var o={createNode:function(e,t,n){var a=new r.default;if(t&&t[Symbol.iterator]){var o=!0,i=!1,s=void 0;try{for(var u,f=t[Symbol.iterator]();!(o=(u=f.next()).done);o=!0){var l=u.value,c=e.createNode(l,n.wrapScalars,null,n);a.items.push(c)}}catch(e){i=!0,s=e}finally{try{o||null==f.return||f.return()}finally{if(i)throw s}}}return a},default:!0,nodeClass:r.default,tag:"tag:yaml.org,2002:seq",resolve:n.default};t.default=o}));a(pe);var ve=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.resolveString=void 0;var n=function(e,t){var n=t.strValue;return n?"string"==typeof n?n:(n.errors.forEach((function(n){n.source||(n.source=t),e.errors.push(n)})),n.str):""};t.resolveString=n;var r={identify:function(e){return"string"==typeof e},default:!0,tag:"tag:yaml.org,2002:str",resolve:n,stringify:function(e,t,n,r){return t=Object.assign({actualString:!0},t),(0,fe.stringifyString)(e,t,n,r)},options:ue.strOptions};t.default=r}));a(ve);ve.resolveString;var ge=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(de),r=o(pe),a=o(ve);function o(e){return e&&e.__esModule?e:{default:e}}var i=[n.default,r.default,a.default];t.default=i}));a(ge);var me=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(z);function r(e){return e&&e.__esModule?e:{default:e}}var a=r(ge).default.concat([{identify:function(e){return null==e},createNode:function(e,t,r){return r.wrapScalars?new n.default(null):null},default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:function(){return null},options:ue.nullOptions,stringify:function(){return ue.nullOptions.nullStr}},{identify:function(e){return"boolean"==typeof e},default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:function(e){return"t"===e[0]||"T"===e[0]},options:ue.boolOptions,stringify:function(e){return e.value?ue.boolOptions.trueStr:ue.boolOptions.falseStr}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o([0-7]+)$/,resolve:function(e,t){return parseInt(t,8)},stringify:function(e){return"0o"+e.value.toString(8)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:function(e){return parseInt(e,10)},stringify:fe.stringifyNumber},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x([0-9a-fA-F]+)$/,resolve:function(e,t){return parseInt(t,16)},stringify:function(e){return"0x"+e.value.toString(16)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.inf|(\.nan))$/i,resolve:function(e,t){return t?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY},stringify:fe.stringifyNumber},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:0|[1-9][0-9]*)(\.[0-9]*)?[eE][-+]?[0-9]+$/,resolve:function(e){return parseFloat(e)},stringify:function(e){var t=e.value;return Number(t).toExponential()}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:0|[1-9][0-9]*)\.([0-9]*)$/,resolve:function(e,t){var r=new n.default(parseFloat(e));return t&&"0"===t[t.length-1]&&(r.minFractionDigits=t.length),r},stringify:fe.stringifyNumber}]);t.default=a}));a(me);var ye=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(de),r=o(pe),a=o(z);function o(e){return e&&e.__esModule?e:{default:e}}var i=[n.default,r.default,{identify:function(e){return"string"==typeof e},default:!0,tag:"tag:yaml.org,2002:str",resolve:ve.resolveString,stringify:function(e){return JSON.stringify(e)}},{identify:function(e){return null==e},createNode:function(e,t,n){return n.wrapScalars?new a.default(null):null},default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:function(){return null},stringify:function(e){return JSON.stringify(e)}},{identify:function(e){return"boolean"==typeof e},default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$/,resolve:function(){return!0},stringify:function(e){return JSON.stringify(e)}},{identify:function(e){return"boolean"==typeof e},default:!0,tag:"tag:yaml.org,2002:bool",test:/^false$/,resolve:function(){return!1},stringify:function(e){return JSON.stringify(e)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:function(e){return parseInt(e,10)},stringify:function(e){return JSON.stringify(e)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:function(e){return parseFloat(e)},stringify:function(e){return JSON.stringify(e)}}];i.scalarFallback=function(e){throw new SyntaxError("Unresolved plain scalar ".concat(JSON.stringify(e)))};var s=i;t.default=s}));a(ye);var _e="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},be=[],we=[],Ee="undefined"!=typeof Uint8Array?Uint8Array:Array,Oe=!1;function Me(){Oe=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0,n=e.length;t<n;++t)be[t]=e[t],we[e.charCodeAt(t)]=t;we["-".charCodeAt(0)]=62,we["_".charCodeAt(0)]=63}function Se(e,t,n){for(var r,a,o=[],i=t;i<n;i+=3)r=(e[i]<<16)+(e[i+1]<<8)+e[i+2],o.push(be[(a=r)>>18&63]+be[a>>12&63]+be[a>>6&63]+be[63&a]);return o.join("")}function Ae(e){var t;Oe||Me();for(var n=e.length,r=n%3,a="",o=[],i=0,s=n-r;i<s;i+=16383)o.push(Se(e,i,i+16383>s?s:i+16383));return 1===r?(t=e[n-1],a+=be[t>>2],a+=be[t<<4&63],a+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],a+=be[t>>10],a+=be[t>>4&63],a+=be[t<<2&63],a+="="),o.push(a),o.join("")}function ke(e,t,n,r,a){var o,i,s=8*a-r-1,u=(1<<s)-1,f=u>>1,l=-7,c=n?a-1:0,d=n?-1:1,h=e[t+c];for(c+=d,o=h&(1<<-l)-1,h>>=-l,l+=s;l>0;o=256*o+e[t+c],c+=d,l-=8);for(i=o&(1<<-l)-1,o>>=-l,l+=r;l>0;i=256*i+e[t+c],c+=d,l-=8);if(0===o)o=1-f;else{if(o===u)return i?NaN:1/0*(h?-1:1);i+=Math.pow(2,r),o-=f}return(h?-1:1)*i*Math.pow(2,o-r)}function Le(e,t,n,r,a,o){var i,s,u,f=8*o-a-1,l=(1<<f)-1,c=l>>1,d=23===a?Math.pow(2,-24)-Math.pow(2,-77):0,h=r?0:o-1,p=r?1:-1,v=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,i=l):(i=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-i))<1&&(i--,u*=2),(t+=i+c>=1?d/u:d*Math.pow(2,1-c))*u>=2&&(i++,u/=2),i+c>=l?(s=0,i=l):i+c>=1?(s=(t*u-1)*Math.pow(2,a),i+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,a),i=0));a>=8;e[n+h]=255&s,h+=p,s/=256,a-=8);for(i=i<<a|s,f+=a;f>0;e[n+h]=255&i,h+=p,i/=256,f-=8);e[n+h-p]|=128*v}var Te={}.toString,Pe=Array.isArray||function(e){return"[object Array]"==Te.call(e)};function Ce(){return xe.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function Ne(e,t){if(Ce()<t)throw new RangeError("Invalid typed array length");return xe.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=xe.prototype:(null===e&&(e=new xe(t)),e.length=t),e}function xe(e,t,n){if(!(xe.TYPED_ARRAY_SUPPORT||this instanceof xe))return new xe(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return Be(this,e)}return Re(this,e,t,n)}function Re(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r);xe.TYPED_ARRAY_SUPPORT?(e=t).__proto__=xe.prototype:e=De(e,t);return e}(e,t,n,r):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!xe.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|Fe(t,n),a=(e=Ne(e,r)).write(t,n);a!==r&&(e=e.slice(0,a));return e}(e,t,n):function(e,t){if(Ye(t)){var n=0|je(t.length);return 0===(e=Ne(e,n)).length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(r=t.length)!=r?Ne(e,0):De(e,t);if("Buffer"===t.type&&Pe(t.data))return De(e,t.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function Ie(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function Be(e,t){if(Ie(t),e=Ne(e,t<0?0:0|je(t)),!xe.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function De(e,t){var n=t.length<0?0:0|je(t.length);e=Ne(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function je(e){if(e>=Ce())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Ce().toString(16)+" bytes");return 0|e}function Ye(e){return!(null==e||!e._isBuffer)}function Fe(e,t){if(Ye(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return ht(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return pt(e).length;default:if(r)return ht(e).length;t=(""+t).toLowerCase(),r=!0}}function Ue(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return nt(this,t,n);case"utf8":case"utf-8":return Ze(this,t,n);case"ascii":return et(this,t,n);case"latin1":case"binary":return tt(this,t,n);case"base64":return ze(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return rt(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function Ke(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function We(e,t,n,r,a){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=a?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(a)return-1;n=e.length-1}else if(n<0){if(!a)return-1;n=0}if("string"==typeof t&&(t=xe.from(t,r)),Ye(t))return 0===t.length?-1:Qe(e,t,n,r,a);if("number"==typeof t)return t&=255,xe.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?a?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):Qe(e,[t],n,r,a);throw new TypeError("val must be string, number or Buffer")}function Qe(e,t,n,r,a){var o,i=1,s=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;i=2,s/=2,u/=2,n/=2}function f(e,t){return 1===i?e[t]:e.readUInt16BE(t*i)}if(a){var l=-1;for(o=n;o<s;o++)if(f(e,o)===f(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*i}else-1!==l&&(o-=o-l),l=-1}else for(n+u>s&&(n=s-u),o=n;o>=0;o--){for(var c=!0,d=0;d<u;d++)if(f(e,o+d)!==f(t,d)){c=!1;break}if(c)return o}return-1}function Ve(e,t,n,r){n=Number(n)||0;var a=e.length-n;r?(r=Number(r))>a&&(r=a):r=a;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var i=0;i<r;++i){var s=parseInt(t.substr(2*i,2),16);if(isNaN(s))return i;e[n+i]=s}return i}function $e(e,t,n,r){return vt(ht(t,e.length-n),e,n,r)}function qe(e,t,n,r){return vt(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function Je(e,t,n,r){return qe(e,t,n,r)}function Ge(e,t,n,r){return vt(pt(t),e,n,r)}function He(e,t,n,r){return vt(function(e,t){for(var n,r,a,o=[],i=0;i<e.length&&!((t-=2)<0);++i)n=e.charCodeAt(i),r=n>>8,a=n%256,o.push(a),o.push(r);return o}(t,e.length-n),e,n,r)}function ze(e,t,n){return 0===t&&n===e.length?Ae(e):Ae(e.slice(t,n))}function Ze(e,t,n){n=Math.min(e.length,n);for(var r=[],a=t;a<n;){var o,i,s,u,f=e[a],l=null,c=f>239?4:f>223?3:f>191?2:1;if(a+c<=n)switch(c){case 1:f<128&&(l=f);break;case 2:128==(192&(o=e[a+1]))&&(u=(31&f)<<6|63&o)>127&&(l=u);break;case 3:o=e[a+1],i=e[a+2],128==(192&o)&&128==(192&i)&&(u=(15&f)<<12|(63&o)<<6|63&i)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:o=e[a+1],i=e[a+2],s=e[a+3],128==(192&o)&&128==(192&i)&&128==(192&s)&&(u=(15&f)<<18|(63&o)<<12|(63&i)<<6|63&s)>65535&&u<1114112&&(l=u)}null===l?(l=65533,c=1):l>65535&&(l-=65536,r.push(l>>>10&1023|55296),l=56320|1023&l),r.push(l),a+=c}return function(e){var t=e.length;if(t<=Xe)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=Xe));return n}(r)}xe.TYPED_ARRAY_SUPPORT=void 0===_e.TYPED_ARRAY_SUPPORT||_e.TYPED_ARRAY_SUPPORT,xe.poolSize=8192,xe._augment=function(e){return e.__proto__=xe.prototype,e},xe.from=function(e,t,n){return Re(null,e,t,n)},xe.TYPED_ARRAY_SUPPORT&&(xe.prototype.__proto__=Uint8Array.prototype,xe.__proto__=Uint8Array),xe.alloc=function(e,t,n){return function(e,t,n,r){return Ie(t),t<=0?Ne(e,t):void 0!==n?"string"==typeof r?Ne(e,t).fill(n,r):Ne(e,t).fill(n):Ne(e,t)}(null,e,t,n)},xe.allocUnsafe=function(e){return Be(null,e)},xe.allocUnsafeSlow=function(e){return Be(null,e)},xe.isBuffer=function(e){return null!=e&&(!!e._isBuffer||gt(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&gt(e.slice(0,0))}(e))},xe.compare=function(e,t){if(!Ye(e)||!Ye(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,a=0,o=Math.min(n,r);a<o;++a)if(e[a]!==t[a]){n=e[a],r=t[a];break}return n<r?-1:r<n?1:0},xe.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},xe.concat=function(e,t){if(!Pe(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return xe.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=xe.allocUnsafe(t),a=0;for(n=0;n<e.length;++n){var o=e[n];if(!Ye(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,a),a+=o.length}return r},xe.byteLength=Fe,xe.prototype._isBuffer=!0,xe.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)Ke(this,t,t+1);return this},xe.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)Ke(this,t,t+3),Ke(this,t+1,t+2);return this},xe.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)Ke(this,t,t+7),Ke(this,t+1,t+6),Ke(this,t+2,t+5),Ke(this,t+3,t+4);return this},xe.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?Ze(this,0,e):Ue.apply(this,arguments)},xe.prototype.equals=function(e){if(!Ye(e))throw new TypeError("Argument must be a Buffer");return this===e||0===xe.compare(this,e)},xe.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},xe.prototype.compare=function(e,t,n,r,a){if(!Ye(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===a&&(a=this.length),t<0||n>e.length||r<0||a>this.length)throw new RangeError("out of range index");if(r>=a&&t>=n)return 0;if(r>=a)return-1;if(t>=n)return 1;if(this===e)return 0;for(var o=(a>>>=0)-(r>>>=0),i=(n>>>=0)-(t>>>=0),s=Math.min(o,i),u=this.slice(r,a),f=e.slice(t,n),l=0;l<s;++l)if(u[l]!==f[l]){o=u[l],i=f[l];break}return o<i?-1:i<o?1:0},xe.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},xe.prototype.indexOf=function(e,t,n){return We(this,e,t,n,!0)},xe.prototype.lastIndexOf=function(e,t,n){return We(this,e,t,n,!1)},xe.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var a=this.length-t;if((void 0===n||n>a)&&(n=a),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return Ve(this,e,t,n);case"utf8":case"utf-8":return $e(this,e,t,n);case"ascii":return qe(this,e,t,n);case"latin1":case"binary":return Je(this,e,t,n);case"base64":return Ge(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return He(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},xe.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Xe=4096;function et(e,t,n){var r="";n=Math.min(e.length,n);for(var a=t;a<n;++a)r+=String.fromCharCode(127&e[a]);return r}function tt(e,t,n){var r="";n=Math.min(e.length,n);for(var a=t;a<n;++a)r+=String.fromCharCode(e[a]);return r}function nt(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var a="",o=t;o<n;++o)a+=dt(e[o]);return a}function rt(e,t,n){for(var r=e.slice(t,n),a="",o=0;o<r.length;o+=2)a+=String.fromCharCode(r[o]+256*r[o+1]);return a}function at(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function ot(e,t,n,r,a,o){if(!Ye(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>a||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function it(e,t,n,r){t<0&&(t=65535+t+1);for(var a=0,o=Math.min(e.length-n,2);a<o;++a)e[n+a]=(t&255<<8*(r?a:1-a))>>>8*(r?a:1-a)}function st(e,t,n,r){t<0&&(t=4294967295+t+1);for(var a=0,o=Math.min(e.length-n,4);a<o;++a)e[n+a]=t>>>8*(r?a:3-a)&255}function ut(e,t,n,r,a,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function ft(e,t,n,r,a){return a||ut(e,0,n,4),Le(e,t,n,r,23,4),n+4}function lt(e,t,n,r,a){return a||ut(e,0,n,8),Le(e,t,n,r,52,8),n+8}xe.prototype.slice=function(e,t){var n,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e),xe.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=xe.prototype;else{var a=t-e;n=new xe(a,void 0);for(var o=0;o<a;++o)n[o]=this[o+e]}return n},xe.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||at(e,t,this.length);for(var r=this[e],a=1,o=0;++o<t&&(a*=256);)r+=this[e+o]*a;return r},xe.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||at(e,t,this.length);for(var r=this[e+--t],a=1;t>0&&(a*=256);)r+=this[e+--t]*a;return r},xe.prototype.readUInt8=function(e,t){return t||at(e,1,this.length),this[e]},xe.prototype.readUInt16LE=function(e,t){return t||at(e,2,this.length),this[e]|this[e+1]<<8},xe.prototype.readUInt16BE=function(e,t){return t||at(e,2,this.length),this[e]<<8|this[e+1]},xe.prototype.readUInt32LE=function(e,t){return t||at(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},xe.prototype.readUInt32BE=function(e,t){return t||at(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},xe.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||at(e,t,this.length);for(var r=this[e],a=1,o=0;++o<t&&(a*=256);)r+=this[e+o]*a;return r>=(a*=128)&&(r-=Math.pow(2,8*t)),r},xe.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||at(e,t,this.length);for(var r=t,a=1,o=this[e+--r];r>0&&(a*=256);)o+=this[e+--r]*a;return o>=(a*=128)&&(o-=Math.pow(2,8*t)),o},xe.prototype.readInt8=function(e,t){return t||at(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},xe.prototype.readInt16LE=function(e,t){t||at(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},xe.prototype.readInt16BE=function(e,t){t||at(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},xe.prototype.readInt32LE=function(e,t){return t||at(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},xe.prototype.readInt32BE=function(e,t){return t||at(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},xe.prototype.readFloatLE=function(e,t){return t||at(e,4,this.length),ke(this,e,!0,23,4)},xe.prototype.readFloatBE=function(e,t){return t||at(e,4,this.length),ke(this,e,!1,23,4)},xe.prototype.readDoubleLE=function(e,t){return t||at(e,8,this.length),ke(this,e,!0,52,8)},xe.prototype.readDoubleBE=function(e,t){return t||at(e,8,this.length),ke(this,e,!1,52,8)},xe.prototype.writeUIntLE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||ot(this,e,t,n,Math.pow(2,8*n)-1,0);var a=1,o=0;for(this[t]=255&e;++o<n&&(a*=256);)this[t+o]=e/a&255;return t+n},xe.prototype.writeUIntBE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||ot(this,e,t,n,Math.pow(2,8*n)-1,0);var a=n-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+n},xe.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,1,255,0),xe.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},xe.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,2,65535,0),xe.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):it(this,e,t,!0),t+2},xe.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,2,65535,0),xe.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):it(this,e,t,!1),t+2},xe.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,4,4294967295,0),xe.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):st(this,e,t,!0),t+4},xe.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,4,4294967295,0),xe.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):st(this,e,t,!1),t+4},xe.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var a=Math.pow(2,8*n-1);ot(this,e,t,n,a-1,-a)}var o=0,i=1,s=0;for(this[t]=255&e;++o<n&&(i*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/i>>0)-s&255;return t+n},xe.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var a=Math.pow(2,8*n-1);ot(this,e,t,n,a-1,-a)}var o=n-1,i=1,s=0;for(this[t+o]=255&e;--o>=0&&(i*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/i>>0)-s&255;return t+n},xe.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,1,127,-128),xe.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},xe.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,2,32767,-32768),xe.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):it(this,e,t,!0),t+2},xe.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,2,32767,-32768),xe.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):it(this,e,t,!1),t+2},xe.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,4,2147483647,-2147483648),xe.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):st(this,e,t,!0),t+4},xe.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||ot(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),xe.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):st(this,e,t,!1),t+4},xe.prototype.writeFloatLE=function(e,t,n){return ft(this,e,t,!0,n)},xe.prototype.writeFloatBE=function(e,t,n){return ft(this,e,t,!1,n)},xe.prototype.writeDoubleLE=function(e,t,n){return lt(this,e,t,!0,n)},xe.prototype.writeDoubleBE=function(e,t,n){return lt(this,e,t,!1,n)},xe.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var a,o=r-n;if(this===e&&n<t&&t<r)for(a=o-1;a>=0;--a)e[a+t]=this[a+n];else if(o<1e3||!xe.TYPED_ARRAY_SUPPORT)for(a=0;a<o;++a)e[a+t]=this[a+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},xe.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var a=e.charCodeAt(0);a<256&&(e=a)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!xe.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var i=Ye(e)?e:ht(new xe(e,r).toString()),s=i.length;for(o=0;o<n-t;++o)this[o+t]=i[o%s]}return this};var ct=/[^+\/0-9A-Za-z-_]/g;function dt(e){return e<16?"0"+e.toString(16):e.toString(16)}function ht(e,t){var n;t=t||1/0;for(var r=e.length,a=null,o=[],i=0;i<r;++i){if((n=e.charCodeAt(i))>55295&&n<57344){if(!a){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(i+1===r){(t-=3)>-1&&o.push(239,191,189);continue}a=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),a=n;continue}n=65536+(a-55296<<10|n-56320)}else a&&(t-=3)>-1&&o.push(239,191,189);if(a=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function pt(e){return function(e){var t,n,r,a,o,i;Oe||Me();var s=e.length;if(s%4>0)throw new Error("Invalid string. Length must be a multiple of 4");o="="===e[s-2]?2:"="===e[s-1]?1:0,i=new Ee(3*s/4-o),r=o>0?s-4:s;var u=0;for(t=0,n=0;t<r;t+=4,n+=3)a=we[e.charCodeAt(t)]<<18|we[e.charCodeAt(t+1)]<<12|we[e.charCodeAt(t+2)]<<6|we[e.charCodeAt(t+3)],i[u++]=a>>16&255,i[u++]=a>>8&255,i[u++]=255&a;return 2===o?(a=we[e.charCodeAt(t)]<<2|we[e.charCodeAt(t+1)]>>4,i[u++]=255&a):1===o&&(a=we[e.charCodeAt(t)]<<10|we[e.charCodeAt(t+1)]<<4|we[e.charCodeAt(t+2)]>>2,i[u++]=a>>8&255,i[u++]=255&a),i}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(ct,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function vt(e,t,n,r){for(var a=0;a<r&&!(a+n>=t.length||a>=e.length);++a)t[a+n]=e[a];return a}function gt(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var mt=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={identify:function(e){return e instanceof Uint8Array},default:!1,tag:"tag:yaml.org,2002:binary",resolve:function(e,t){var n=(0,ve.resolveString)(e,t);return xe.from(n,"base64")},options:ue.binaryOptions,stringify:function(e,t,n,r){var a,o=e.comment,i=e.type,s=e.value;if(a=s instanceof xe?s.toString("base64"):xe.from(s.buffer).toString("base64"),i||(i=ue.binaryOptions.defaultType),i===T.Type.QUOTE_DOUBLE)s=a;else{for(var u=ue.binaryOptions.lineWidth,f=Math.ceil(a.length/u),l=new Array(f),c=0,d=0;c<f;++c,d+=u)l[c]=a.substr(d,u);s=l.join(i===T.Type.BLOCK_LITERAL?"\n":" ")}return(0,fe.stringifyString)({comment:o,type:i,value:s},t,n,r)}};t.default=n}));a(mt);var yt=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.parsePairs=s,t.createPairs=u,t.default=void 0;var n=i(te),r=i(Z),a=i(he),o=i(ne);function i(e){return e&&e.__esModule?e:{default:e}}function s(e,t){for(var o=(0,a.default)(e,t),i=0;i<o.items.length;++i){var s=o.items[i];if(!(s instanceof r.default)){if(s instanceof n.default){if(s.items.length>1){throw new x.YAMLSemanticError(t,"Each pair must have its own sequence indicator")}var u=s.items[0]||new r.default;s.commentBefore&&(u.commentBefore=u.commentBefore?"".concat(s.commentBefore,"\n").concat(u.commentBefore):s.commentBefore),s.comment&&(u.comment=u.comment?"".concat(s.comment,"\n").concat(u.comment):s.comment),s=u}o.items[i]=s instanceof r.default?s:new r.default(s)}}return o}function u(e,t,n){var r=new o.default;r.tag="tag:yaml.org,2002:pairs";var a=!0,i=!1,s=void 0;try{for(var u,f=t[Symbol.iterator]();!(a=(u=f.next()).done);a=!0){var l=u.value,c=void 0,d=void 0;if(Array.isArray(l)){if(2!==l.length)throw new TypeError("Expected [key, value] tuple: ".concat(l));c=l[0],d=l[1]}else if(l&&l instanceof Object){var h=Object.keys(l);if(1!==h.length)throw new TypeError("Expected { key: value } tuple: ".concat(l));d=l[c=h[0]]}else c=l;var p=e.createPair(c,d,n);r.items.push(p)}}catch(e){i=!0,s=e}finally{try{a||null==f.return||f.return()}finally{if(i)throw s}}return r}var f={default:!1,tag:"tag:yaml.org,2002:pairs",resolve:s,createNode:u};t.default=f}));a(yt);yt.parsePairs,yt.createPairs;var _t=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.YAMLOMap=void 0;var n=i(G),r=i(te),a=i(Z),o=i(z);function i(e){return e&&e.__esModule?e:{default:e}}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=function(e){function t(){var e;return s(this,t),u(v(e=g(this,c(t).call(this))),"add",r.default.prototype.add.bind(v(e))),u(v(e),"delete",r.default.prototype.delete.bind(v(e))),u(v(e),"get",r.default.prototype.get.bind(v(e))),u(v(e),"has",r.default.prototype.has.bind(v(e))),u(v(e),"set",r.default.prototype.set.bind(v(e))),e.tag=t.tag,e}return l(t,e),f(t,[{key:"toJSON",value:function(e,t){var r=new Map;t&&t.onCreate&&t.onCreate(r);var o=!0,i=!1,s=void 0;try{for(var u,f=this.items[Symbol.iterator]();!(o=(u=f.next()).done);o=!0){var l=u.value,c=void 0,d=void 0;if(l instanceof a.default?(c=(0,n.default)(l.key,"",t),d=(0,n.default)(l.value,c,t)):c=(0,n.default)(l,"",t),r.has(c))throw new Error("Ordered maps must not include duplicate keys");r.set(c,d)}}catch(e){i=!0,s=e}finally{try{o||null==f.return||f.return()}finally{if(i)throw s}}return r}}]),t}(i(ne).default);t.YAMLOMap=d,u(d,"tag","tag:yaml.org,2002:omap");var h={identify:function(e){return e instanceof Map},nodeClass:d,default:!1,tag:"tag:yaml.org,2002:omap",resolve:function(e,t){var n=(0,yt.parsePairs)(e,t),r=[],a=!0,i=!1,s=void 0;try{for(var u,f=n.items[Symbol.iterator]();!(a=(u=f.next()).done);a=!0){var l=u.value.key;if(l instanceof o.default){if(r.includes(l.value)){throw new x.YAMLSemanticError(t,"Ordered maps must not include duplicate keys")}r.push(l.value)}}}catch(e){i=!0,s=e}finally{try{a||null==f.return||f.return()}finally{if(i)throw s}}return Object.assign(new d,n)},createNode:function(e,t,n){var r=(0,yt.createPairs)(e,t,n),a=new d;return a.items=r.items,a}};t.default=h}));a(_t);_t.YAMLOMap;var bt=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.YAMLSet=void 0;var n=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}return t.default=e,t}(te),r=u(Z),a=u(ce),o=u(z);function u(e){return e&&e.__esModule?e:{default:e}}var d,h,p,v=function(e){function t(){var e;return s(this,t),(e=g(this,c(t).call(this))).tag=t.tag,e}return l(t,e),f(t,[{key:"add",value:function(e){var t=e instanceof r.default?e:new r.default(e);(0,n.findPair)(this.items,t.key)||this.items.push(t)}},{key:"get",value:function(e,t){var a=(0,n.findPair)(this.items,e);return!t&&a instanceof r.default?a.key instanceof o.default?a.key.value:a.key:a}},{key:"set",value:function(e,t){if("boolean"!=typeof t)throw new Error("Expected boolean value for set(key, value) in a YAML set, not ".concat(i(t)));var a=(0,n.findPair)(this.items,e);a&&!t?this.items.splice(this.items.indexOf(a),1):!a&&t&&this.items.push(new r.default(e))}},{key:"toJSON",value:function(e,n){return m(c(t.prototype),"toJSON",this).call(this,e,n,Set)}},{key:"toString",value:function(e,n,r){if(!e)return JSON.stringify(this);if(this.hasAllNullValues())return m(c(t.prototype),"toString",this).call(this,e,n,r);throw new Error("Set items must all have null values")}}]),t}(n.default);t.YAMLSet=v,p="tag:yaml.org,2002:set",(h="tag")in(d=v)?Object.defineProperty(d,h,{value:p,enumerable:!0,configurable:!0,writable:!0}):d[h]=p;var y={identify:function(e){return e instanceof Set},nodeClass:v,default:!1,tag:"tag:yaml.org,2002:set",resolve:function(e,t){var n=(0,a.default)(e,t);if(!n.hasAllNullValues())throw new x.YAMLSemanticError(t,"Set items must all have null values");return Object.assign(new v,n)},createNode:function(e,t,n){var r=new v,a=!0,o=!1,i=void 0;try{for(var s,u=t[Symbol.iterator]();!(a=(s=u.next()).done);a=!0){var f=s.value;r.items.push(e.createPair(f,null,n))}}catch(e){o=!0,i=e}finally{try{a||null==u.return||u.return()}finally{if(o)throw i}}return r}};t.default=y}));a(bt);bt.YAMLSet;var wt=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.timestamp=t.floatTime=t.intTime=void 0;var n=function(e,t){var n=t.split(":").reduce((function(e,t){return 60*e+Number(t)}),0);return"-"===e?-n:n},r=function(e){var t=e.value;if(isNaN(t)||!isFinite(t))return(0,fe.stringifyNumber)(t);var n="";t<0&&(n="-",t=Math.abs(t));var r=[t%60];return t<60?r.unshift(0):(t=Math.round((t-r[0])/60),r.unshift(t%60),t>=60&&(t=Math.round((t-r[0])/60),r.unshift(t))),n+r.map((function(e){return e<10?"0"+String(e):String(e)})).join(":").replace(/000000\d*$/,"")},a={identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^([-+]?)([0-9][0-9_]*(?::[0-5]?[0-9])+)$/,resolve:function(e,t,r){return n(t,r.replace(/_/g,""))},stringify:r};t.intTime=a;var o={identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^([-+]?)([0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*)$/,resolve:function(e,t,r){return n(t,r.replace(/_/g,""))},stringify:r};t.floatTime=o;var i={identify:function(e){return e instanceof Date},default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^(?:([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?)$"),resolve:function(e,t,r,a,o,i,s,u,f){u&&(u=(u+"00").substr(1,3));var l=Date.UTC(t,r-1,a,o||0,i||0,s||0,u||0);if(f&&"Z"!==f){var c=n(f[0],f.slice(1));Math.abs(c)<30&&(c*=60),l-=6e4*c}return new Date(l)},stringify:function(e){return e.value.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")}};t.timestamp=i}));a(wt);wt.timestamp,wt.floatTime,wt.intTime;var Et=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=u(z),r=u(ge),a=u(mt),o=u(_t),i=u(yt),s=u(bt);function u(e){return e&&e.__esModule?e:{default:e}}var f=r.default.concat([{identify:function(e){return null==e},createNode:function(e,t,r){return r.wrapScalars?new n.default(null):null},default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:function(){return null},options:ue.nullOptions,stringify:function(){return ue.nullOptions.nullStr}},{identify:function(e){return"boolean"==typeof e},default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:function(){return!0},options:ue.boolOptions,stringify:function(e){return e.value?ue.boolOptions.trueStr:ue.boolOptions.falseStr}},{identify:function(e){return"boolean"==typeof e},default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/i,resolve:function(){return!1},options:ue.boolOptions,stringify:function(e){return e.value?ue.boolOptions.trueStr:ue.boolOptions.falseStr}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^0b([0-1_]+)$/,resolve:function(e,t){return parseInt(t.replace(/_/g,""),2)},stringify:function(e){return"0b"+e.value.toString(2)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0([0-7_]+)$/,resolve:function(e,t){return parseInt(t.replace(/_/g,""),8)},stringify:function(e){var t=e.value;return(t<0?"-0":"0")+t.toString(8)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:function(e){return parseInt(e.replace(/_/g,""),10)},stringify:fe.stringifyNumber},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x([0-9a-fA-F_]+)$/,resolve:function(e,t){return parseInt(t.replace(/_/g,""),16)},stringify:function(e){var t=e.value;return(t<0?"-0x":"0x")+t.toString(16)}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.inf|(\.nan))$/i,resolve:function(e,t){return t?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY},stringify:fe.stringifyNumber},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?([0-9][0-9_]*)?(\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:function(e){return parseFloat(e.replace(/_/g,""))},stringify:function(e){var t=e.value;return Number(t).toExponential()}},{identify:function(e){return"number"==typeof e},default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.([0-9_]*)$/,resolve:function(e,t){var r=new n.default(parseFloat(e.replace(/_/g,"")));if(t){var a=t.replace(/_/g,"");"0"===a[a.length-1]&&(r.minFractionDigits=a.length)}return r},stringify:fe.stringifyNumber}],a.default,o.default,i.default,s.default,wt.intTime,wt.floatTime,wt.timestamp);t.default=f}));a(Et);var Ot=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.tags=t.schemas=void 0;var n=d(me),r=d(ge),a=d(ye),o=d(Et),i=d(de),s=d(pe),u=d(mt),f=d(_t),l=d(yt),c=d(bt);function d(e){return e&&e.__esModule?e:{default:e}}var h={core:n.default,failsafe:r.default,json:a.default,yaml11:o.default};t.schemas=h;var p={binary:u.default,floatTime:wt.floatTime,intTime:wt.intTime,map:i.default,omap:f.default,pairs:l.default,seq:s.default,set:c.default,timestamp:wt.timestamp};t.tags=p}));a(Ot);Ot.tags,Ot.schemas;var Mt=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=l(ee),r=l(X),a=l(H),o=l(Z),u=l(z);function l(e){return e&&e.__esModule?e:{default:e}}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=function(){function e(t){var n=t.customTags,r=t.merge,a=t.schema,o=t.tags;if(s(this,e),this.merge=!!r,this.name=a,this.tags=Ot.schemas[a.replace(/\W/g,"")],!this.tags){var i=Object.keys(Ot.schemas).map((function(e){return JSON.stringify(e)})).join(", ");throw new Error('Unknown schema "'.concat(a,'"; use one of ').concat(i))}if(!n&&o&&(n=o,(0,ie.warnOptionDeprecation)("tags","customTags")),Array.isArray(n)){var u=!0,f=!1,l=void 0;try{for(var c,d=n[Symbol.iterator]();!(u=(c=d.next()).done);u=!0){var h=c.value;this.tags=this.tags.concat(h)}}catch(e){f=!0,l=e}finally{try{u||null==d.return||d.return()}finally{if(f)throw l}}}else"function"==typeof n&&(this.tags=n(this.tags.slice()));for(var p=0;p<this.tags.length;++p){var v=this.tags[p];if("string"==typeof v){var g=Ot.tags[v];if(!g){var m=Object.keys(Ot.tags).map((function(e){return JSON.stringify(e)})).join(", ");throw new Error('Unknown custom tag "'.concat(v,'"; use one of ').concat(m))}this.tags[p]=g}}}return f(e,[{key:"createNode",value:function(t,r,a,o){var s;if(a){a.startsWith("!!")&&(a=e.defaultPrefix+a.slice(2));var f=this.tags.filter((function(e){return e.tag===a}));if(!(s=f.find((function(e){return!e.format}))||f[0]))throw new Error("Tag ".concat(a," not found"))}else if(!(s=this.tags.find((function(e){return(e.identify&&e.identify(t)||e.class&&t instanceof e.class)&&!e.format})))){if("function"==typeof t.toJSON&&(t=t.toJSON()),"object"!==i(t))return r?new u.default(t):t;s=t instanceof Map?Ot.tags.map:t[Symbol.iterator]?Ot.tags.seq:Ot.tags.map}o?o.wrapScalars=r:o={wrapScalars:r},o.onTagObj&&(o.onTagObj(s),delete o.onTagObj);var l={};if(t&&"object"===i(t)&&o.prevObjects){var c=o.prevObjects.find((function(e){return e.value===t}));if(c){var d=new n.default(c);return o.aliasNodes.push(d),d}l.value=t,o.prevObjects.push(l)}return l.node=s.createNode?s.createNode(this,t,o):r?new u.default(t):t,l.node}},{key:"createPair",value:function(e,t,n){var r=this.createNode(e,n.wrapScalars,null,n),a=this.createNode(t,n.wrapScalars,null,n);return new o.default(r,a)}},{key:"resolveScalar",value:function(e,t){t||(t=this.tags);for(var n=0;n<t.length;++n){var r=t[n],a=r.format,o=r.test,i=r.resolve;if(o){var s=e.match(o);if(s){var f=i.apply(null,s);return f instanceof u.default||(f=new u.default(f)),a&&(f.format=a),f}}}return this.tags.scalarFallback&&(e=this.tags.scalarFallback(e)),new u.default(e)}},{key:"resolveNode",value:function(e,t,n){var a=this.tags.filter((function(e){return e.tag===n})),o=a.find((function(e){return!e.test}));t.error&&e.errors.push(t.error);try{if(o){var i=o.resolve(e,t);i instanceof r.default||(i=new u.default(i)),t.resolved=i}else{var s=(0,ve.resolveString)(e,t);"string"==typeof s&&a.length>0&&(t.resolved=this.resolveScalar(s,a))}}catch(n){n.source||(n.source=t),e.errors.push(n),t.resolved=null}return t.resolved?(n&&t.tag&&(t.resolved.tag=n),t.resolved):null}},{key:"resolveNodeWithFallback",value:function(t,n,r){var a=this.resolveNode(t,n,r);if(Object.prototype.hasOwnProperty.call(n,"resolved"))return a;var o,i=(o=n.type)===T.Type.FLOW_MAP||o===T.Type.MAP?e.defaultTags.MAP:function(e){var t=e.type;return t===T.Type.FLOW_SEQ||t===T.Type.SEQ}(n)?e.defaultTags.SEQ:e.defaultTags.STR;if(i){t.warnings.push(new x.YAMLWarning(n,"The tag ".concat(r," is unavailable, falling back to ").concat(i)));var s=this.resolveNode(t,n,i);return s.tag=r,s}return t.errors.push(new x.YAMLReferenceError(n,"The tag ".concat(r," is unavailable"))),null}},{key:"getTagObject",value:function(e){if(e instanceof n.default)return n.default;if(e.tag){var t=this.tags.filter((function(t){return t.tag===e.tag}));if(t.length>0)return t.find((function(t){return t.format===e.format}))||t[0]}var r,a;if(e instanceof u.default){a=e.value;var o=this.tags.filter((function(e){return e.identify&&e.identify(a)||e.class&&a instanceof e.class}));r=o.find((function(t){return t.format===e.format}))||o.find((function(e){return!e.format}))}else a=e,r=this.tags.find((function(e){return e.nodeClass&&a instanceof e.nodeClass}));if(!r){var s=a&&a.constructor?a.constructor.name:i(a);throw new Error("Tag not resolved for ".concat(s," value"))}return r}},{key:"stringifyProps",value:function(e,t,n){var r=n.anchors,a=n.doc,o=[],i=a.anchors.getName(e);return i&&(r[i]=e,o.push("&".concat(i))),e.tag?o.push(a.stringifyTag(e.tag)):t.default||o.push(a.stringifyTag(t.tag)),o.join(" ")}},{key:"stringify",value:function(e,t,n,i){var s;if(!(e instanceof a.default)){var u={aliasNodes:[],onTagObj:function(e){return s=e},prevObjects:[]};e=this.createNode(e,!0,null,u);var f=t.doc.anchors,l=!0,c=!1,d=void 0;try{for(var h,p=u.aliasNodes[Symbol.iterator]();!(l=(h=p.next()).done);l=!0){var v=h.value;v.source=v.source.node;var g=f.getName(v.source);g||(g=f.newName(),f.map[g]=v.source)}}catch(e){c=!0,d=e}finally{try{l||null==p.return||p.return()}finally{if(c)throw d}}}if(t.tags=this,e instanceof o.default)return e.toString(t,n,i);s||(s=this.getTagObject(e));var m=this.stringifyProps(e,s,t),y="function"==typeof s.stringify?s.stringify(e,t,n,i):e instanceof r.default?e.toString(t,n,i):(0,fe.stringifyString)(e,t,n,i);return m?e instanceof r.default&&"{"!==y[0]&&"["!==y[0]?"".concat(m,"\n").concat(t.indent).concat(y):"".concat(m," ").concat(y):y}}]),e}();t.default=d,c(d,"defaultPrefix","tag:yaml.org,2002:"),c(d,"defaultTags",{MAP:"tag:yaml.org,2002:map",SEQ:"tag:yaml.org,2002:seq",STR:"tag:yaml.org,2002:str"})}));a(Mt);var St=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=h(J),r=h(ae),a=h(oe),o=h(Mt),i=h(ee),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}return t.default=e,t}(X),l=h(H),c=h(z),d=h(G);function h(e){return e&&e.__esModule?e:{default:e}}var p,v,g,m=function(){function e(t){s(this,e),this.anchors=new r.default(t.anchorPrefix),this.commentBefore=null,this.comment=null,this.contents=null,this.directivesEndMarker=null,this.errors=[],this.options=t,this.schema=null,this.tagPrefixes=[],this.version=null,this.warnings=[]}return f(e,[{key:"assertCollectionContents",value:function(){if(this.contents instanceof u.default)return!0;throw new Error("Expected a YAML collection as document contents")}},{key:"add",value:function(e){return this.assertCollectionContents(),this.contents.add(e)}},{key:"addIn",value:function(e,t){this.assertCollectionContents(),this.contents.addIn(e,t)}},{key:"delete",value:function(e){return this.assertCollectionContents(),this.contents.delete(e)}},{key:"deleteIn",value:function(e){return(0,u.isEmptyPath)(e)?null!=this.contents&&(this.contents=null,!0):(this.assertCollectionContents(),this.contents.deleteIn(e))}},{key:"getDefaults",value:function(){return e.defaults[this.version]||e.defaults[this.options.version]||{}}},{key:"get",value:function(e,t){return this.contents instanceof u.default?this.contents.get(e,t):void 0}},{key:"getIn",value:function(e,t){return(0,u.isEmptyPath)(e)?!t&&this.contents instanceof c.default?this.contents.value:this.contents:this.contents instanceof u.default?this.contents.getIn(e,t):void 0}},{key:"has",value:function(e){return this.contents instanceof u.default&&this.contents.has(e)}},{key:"hasIn",value:function(e){return(0,u.isEmptyPath)(e)?void 0!==this.contents:this.contents instanceof u.default&&this.contents.hasIn(e)}},{key:"set",value:function(e,t){this.assertCollectionContents(),this.contents.set(e,t)}},{key:"setIn",value:function(e,t){(0,u.isEmptyPath)(e)?this.contents=t:(this.assertCollectionContents(),this.contents.setIn(e,t))}},{key:"setSchema",value:function(e,t){if(e||t||!this.schema){"number"==typeof e&&(e=e.toFixed(1)),"1.0"===e||"1.1"===e||"1.2"===e?(this.version?this.version=e:this.options.version=e,delete this.options.schema):e&&"string"==typeof e&&(this.options.schema=e),Array.isArray(t)&&(this.options.customTags=t);var n=Object.assign({},this.getDefaults(),this.options);this.schema=new o.default(n)}}},{key:"parse",value:function(e,t){this.options.keepCstNodes&&(this.cstNode=e),this.options.keepNodeTypes&&(this.type="DOCUMENT");var n=e.directives,r=void 0===n?[]:n,a=e.contents,o=void 0===a?[]:a,i=e.directivesEndMarker,s=e.error,u=e.valueRange;if(s&&(s.source||(s.source=this),this.errors.push(s)),this.parseDirectives(r,t),i&&(this.directivesEndMarker=!0),this.range=u?[u.start,u.end]:null,this.setSchema(),this.anchors._cstAliases=[],this.parseContents(o),this.anchors.resolveNodes(),this.options.prettyErrors){var f=!0,l=!1,c=void 0;try{for(var d,h=this.errors[Symbol.iterator]();!(f=(d=h.next()).done);f=!0){var p=d.value;p instanceof x.YAMLError&&p.makePretty()}}catch(e){l=!0,c=e}finally{try{f||null==h.return||h.return()}finally{if(l)throw c}}var v=!0,g=!1,m=void 0;try{for(var y,_=this.warnings[Symbol.iterator]();!(v=(y=_.next()).done);v=!0){var b=y.value;b instanceof x.YAMLError&&b.makePretty()}}catch(e){g=!0,m=e}finally{try{v||null==_.return||_.return()}finally{if(g)throw m}}}return this}},{key:"parseDirectives",value:function(e,t){var n=this,r=[],a=!1;if(e.forEach((function(e){var t=e.comment,o=e.name;switch(o){case"TAG":n.resolveTagDirective(e),a=!0;break;case"YAML":case"YAML:1.0":n.resolveYamlDirective(e),a=!0;break;default:if(o){var i="YAML only supports %TAG and %YAML directives, and not %".concat(o);n.warnings.push(new x.YAMLWarning(e,i))}}t&&r.push(t)})),t&&!a&&"1.1"===(this.version||t.version||this.options.version)){this.tagPrefixes=t.tagPrefixes.map((function(e){return{handle:e.handle,prefix:e.prefix}})),this.version=t.version}this.commentBefore=r.join("\n")||null}},{key:"parseContents",value:function(e){var t=this,n={before:[],after:[]},r=[],a=!1;switch(e.forEach((function(e){if(e.valueRange){if(1===r.length){t.errors.push(new x.YAMLSyntaxError(e,"Document is not valid YAML (bad indentation?)"))}var o=t.resolveNode(e);a&&(o.spaceBefore=!0,a=!1),r.push(o)}else if(null!==e.comment){(0===r.length?n.before:n.after).push(e.comment)}else e.type===T.Type.BLANK_LINE&&(a=!0,0===r.length&&n.before.length>0&&!t.commentBefore&&(t.commentBefore=n.before.join("\n"),n.before=[]))})),r.length){case 0:this.contents=null,n.after=n.before;break;case 1:if(this.contents=r[0],this.contents){var o=n.before.join("\n")||null;if(o){var i=this.contents instanceof u.default&&this.contents.items[0]?this.contents.items[0]:this.contents;i.commentBefore=i.commentBefore?"".concat(o,"\n").concat(i.commentBefore):o}}else n.after=n.before.concat(n.after);break;default:this.contents=r,this.contents[0]?this.contents[0].commentBefore=n.before.join("\n")||null:n.after=n.before.concat(n.after)}this.comment=n.after.join("\n")||null}},{key:"resolveTagDirective",value:function(e){var t=y(e.parameters,2),n=t[0],r=t[1];if(n&&r)if(this.tagPrefixes.every((function(e){return e.handle!==n})))this.tagPrefixes.push({handle:n,prefix:r});else{this.errors.push(new x.YAMLSemanticError(e,"The %TAG directive must only be given at most once per handle in the same document."))}else{this.errors.push(new x.YAMLSemanticError(e,"Insufficient parameters given for %TAG directive"))}}},{key:"resolveYamlDirective",value:function(t){var n=y(t.parameters,1)[0];if("YAML:1.0"===t.name&&(n="1.0"),this.version){this.errors.push(new x.YAMLSemanticError(t,"The %YAML directive must only be given at most once per document."))}if(n){if(!e.defaults[n]){var r=this.version||this.options.version,a="Document will be parsed as YAML ".concat(r," rather than YAML ").concat(n);this.warnings.push(new x.YAMLWarning(t,a))}this.version=n}else{this.errors.push(new x.YAMLSemanticError(t,"Insufficient parameters given for %YAML directive"))}}},{key:"resolveTagName",value:function(e){var t=e.tag,n=e.type,r=!1;if(t){var a=t.handle,i=t.suffix,s=t.verbatim;if(s){if("!"!==s&&"!!"!==s)return s;var u="Verbatim tags aren't resolved, so ".concat(s," is invalid.");this.errors.push(new x.YAMLSemanticError(e,u))}else if("!"!==a||i){var f=this.tagPrefixes.find((function(e){return e.handle===a}));if(!f){var l=this.getDefaults().tagPrefixes;l&&(f=l.find((function(e){return e.handle===a})))}if(f){if(i){if("!"===a&&"1.0"===(this.version||this.options.version)){if("^"===i[0])return i;if(/[:/]/.test(i)){var c=i.match(/^([a-z0-9-]+)\/(.*)/i);return c?"tag:".concat(c[1],".yaml.org,2002:").concat(c[2]):"tag:".concat(i)}}return f.prefix+decodeURIComponent(i)}this.errors.push(new x.YAMLSemanticError(e,"The ".concat(a," tag has no suffix.")))}else{var d="The ".concat(a," tag handle is non-default and was not declared.");this.errors.push(new x.YAMLSemanticError(e,d))}}else r=!0}switch(n){case T.Type.BLOCK_FOLDED:case T.Type.BLOCK_LITERAL:case T.Type.QUOTE_DOUBLE:case T.Type.QUOTE_SINGLE:return o.default.defaultTags.STR;case T.Type.FLOW_MAP:case T.Type.MAP:return o.default.defaultTags.MAP;case T.Type.FLOW_SEQ:case T.Type.SEQ:return o.default.defaultTags.SEQ;case T.Type.PLAIN:return r?o.default.defaultTags.STR:null;default:return null}}},{key:"resolveNode",value:function(e){if(!e)return null;var t,n=this.anchors,r=this.errors,a=this.schema,o=!1,s=!1,u={before:[],after:[]},f=function(e){return e&&-1!==[T.Type.MAP_KEY,T.Type.MAP_VALUE,T.Type.SEQ_ITEM].indexOf(e.type)}(e.context.parent)?e.context.parent.props.concat(e.props):e.props,l=!0,c=!1,d=void 0;try{for(var h,p=f[Symbol.iterator]();!(l=(h=p.next()).done);l=!0){var v=h.value,g=v.start,m=v.end;switch(e.context.src[g]){case T.Char.COMMENT:if(!e.commentHasRequiredWhitespace(g)){r.push(new x.YAMLSemanticError(e,"Comments must be separated from other tokens by white space characters"))}var y=e.context.src.slice(g+1,m),_=e.header,b=e.valueRange;b&&(g>b.start||_&&g>_.start)?u.after.push(y):u.before.push(y);break;case T.Char.ANCHOR:if(o){r.push(new x.YAMLSemanticError(e,"A node can have at most one anchor"))}o=!0;break;case T.Char.TAG:if(s){r.push(new x.YAMLSemanticError(e,"A node can have at most one tag"))}s=!0}}}catch(e){c=!0,d=e}finally{try{l||null==p.return||p.return()}finally{if(c)throw d}}if(o){var w=e.anchor,E=n.getNode(w);E&&(n.map[n.newName(w)]=E),n.map[w]=e}if(e.type===T.Type.ALIAS){if(o||s){r.push(new x.YAMLSemanticError(e,"An alias node must not specify any properties"))}var O=e.rawValue,M=n.getNode(O);if(!M){var S="Aliased anchor not found: ".concat(O);return r.push(new x.YAMLReferenceError(e,S)),null}t=new i.default(M),n._cstAliases.push(t)}else{var A=this.resolveTagName(e);if(A)t=a.resolveNodeWithFallback(this,e,A);else{if(e.type!==T.Type.PLAIN){var k="Failed to resolve ".concat(e.type," node here");return r.push(new x.YAMLSyntaxError(e,k)),null}try{t=a.resolveScalar(e.strValue||"")}catch(t){return t.source||(t.source=e),r.push(t),null}}}if(t){t.range=[e.range.start,e.range.end],this.options.keepCstNodes&&(t.cstNode=e),this.options.keepNodeTypes&&(t.type=e.type);var L=u.before.join("\n");L&&(t.commentBefore=t.commentBefore?"".concat(t.commentBefore,"\n").concat(L):L);var P=u.after.join("\n");P&&(t.comment=t.comment?"".concat(t.comment,"\n").concat(P):P)}return e.resolved=t}},{key:"listNonDefaultTags",value:function(){return(0,a.default)(this.contents).filter((function(e){return 0!==e.indexOf(o.default.defaultPrefix)}))}},{key:"setTagPrefix",value:function(e,t){if("!"!==e[0]||"!"!==e[e.length-1])throw new Error("Handle must start and end with !");if(t){var n=this.tagPrefixes.find((function(t){return t.handle===e}));n?n.prefix=t:this.tagPrefixes.push({handle:e,prefix:t})}else this.tagPrefixes=this.tagPrefixes.filter((function(t){return t.handle!==e}))}},{key:"stringifyTag",value:function(e){if("1.0"===(this.version||this.options.version)){var t=e.match(/^tag:private\.yaml\.org,2002:([^:/]+)$/);if(t)return"!"+t[1];var n=e.match(/^tag:([a-zA-Z0-9-]+)\.yaml\.org,2002:(.*)/);return n?"!".concat(n[1],"/").concat(n[2]):"!".concat(e.replace(/^tag:/,""))}var r=this.tagPrefixes.find((function(t){return 0===e.indexOf(t.prefix)}));if(!r){var a=this.getDefaults().tagPrefixes;r=a&&a.find((function(t){return 0===e.indexOf(t.prefix)}))}if(!r)return"!"===e[0]?e:"!<".concat(e,">");var o=e.substr(r.prefix.length).replace(/[!,[\]{}]/g,(function(e){return{"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"}[e]}));return r.handle+o}},{key:"toJSON",value:function(e){var t=this,n=this.options,r=n.keepBlobsInJSON,a=n.mapAsMap,o=n.maxAliasCount,i=r&&("string"!=typeof e||!(this.contents instanceof c.default)),s={doc:this,keep:i,mapAsMap:i&&!!a,maxAliasCount:o},u=Object.keys(this.anchors.map);return u.length>0&&(s.anchors=u.map((function(e){return{alias:[],aliasCount:0,count:1,node:t.anchors.map[e]}}))),(0,d.default)(this.contents,e,s)}},{key:"toString",value:function(){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");this.setSchema();var e=[],t=!1;if(this.version){var r="%YAML 1.2";"yaml-1.1"===this.schema.name&&("1.0"===this.version?r="%YAML:1.0":"1.1"===this.version&&(r="%YAML 1.1")),e.push(r),t=!0}var a=this.listNonDefaultTags();this.tagPrefixes.forEach((function(n){var r=n.handle,o=n.prefix;a.some((function(e){return 0===e.indexOf(o)}))&&(e.push("%TAG ".concat(r," ").concat(o)),t=!0)})),(t||this.directivesEndMarker)&&e.push("---"),this.commentBefore&&(!t&&this.directivesEndMarker||e.unshift(""),e.unshift(this.commentBefore.replace(/^/gm,"#")));var o={anchors:{},doc:this,indent:""},i=!1,s=null;if(this.contents){this.contents instanceof l.default&&(this.contents.spaceBefore&&(t||this.directivesEndMarker)&&e.push(""),this.contents.commentBefore&&e.push(this.contents.commentBefore.replace(/^/gm,"#")),o.forceBlockIndent=!!this.comment,s=this.contents.comment);var u=s?null:function(){return i=!0},f=this.schema.stringify(this.contents,o,(function(){return s=null}),u);e.push((0,n.default)(f,"",s))}else void 0!==this.contents&&e.push(this.schema.stringify(this.contents,o));return this.comment&&(i&&!s||""===e[e.length-1]||e.push(""),e.push(this.comment.replace(/^/gm,"#"))),e.join("\n")+"\n"}}]),e}();t.default=m,p=m,v="defaults",g={"1.0":{schema:"yaml-1.1",merge:!0,tagPrefixes:[{handle:"!",prefix:o.default.defaultPrefix},{handle:"!!",prefix:"tag:private.yaml.org,2002:"}]},1.1:{schema:"yaml-1.1",merge:!0,tagPrefixes:[{handle:"!",prefix:"!"},{handle:"!!",prefix:o.default.defaultPrefix}]},1.2:{schema:"core",merge:!1,tagPrefixes:[{handle:"!",prefix:"!"},{handle:"!!",prefix:o.default.defaultPrefix}]}},v in p?Object.defineProperty(p,v,{value:g,enumerable:!0,configurable:!0,writable:!0}):p[v]=g}));a(St);var At=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(q),r=o(St),a=o(Mt);function o(e){return e&&e.__esModule?e:{default:e}}var i={anchorPrefix:"a",customTags:null,keepCstNodes:!1,keepNodeTypes:!0,keepBlobsInJSON:!0,mapAsMap:!1,maxAliasCount:100,prettyErrors:!1,simpleKeys:!1,version:"1.2"};var u=function(e){function t(e){return s(this,t),g(this,c(t).call(this,Object.assign({},i,e)))}return l(t,e),t}(r.default);function f(e,t){var r=(0,n.default)(e),a=new u(t).parse(r[0]);if(r.length>1){a.errors.unshift(new x.YAMLSemanticError(r[1],"Source contains multiple documents; please use YAML.parseAllDocuments()"))}return a}var d={createNode:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2?arguments[2]:void 0;void 0===n&&"string"==typeof t&&(n=t,t=!0);var o=Object.assign({},r.default.defaults[i.version],i);return new a.default(o).createNode(e,t,n)},defaultOptions:i,Document:u,parse:function(e,t){var n=f(e,t);if(n.warnings.forEach((function(e){return(0,ie.warn)(e)})),n.errors.length>0)throw n.errors[0];return n.toJSON()},parseAllDocuments:function(e,t){var r,a=[],o=!0,i=!1,s=void 0;try{for(var f,l=(0,n.default)(e)[Symbol.iterator]();!(o=(f=l.next()).done);o=!0){var c=f.value,d=new u(t);d.parse(c,r),a.push(d),r=d}}catch(e){i=!0,s=e}finally{try{o||null==l.return||l.return()}finally{if(i)throw s}}return a},parseCST:n.default,parseDocument:f,stringify:function(e,t){var n=new u(t);return n.contents=e,String(n)}};t.default=d}));a(At);var kt=At.default,Lt=o((function(e,t){t.__esModule=!0,t.defineParents=function e(t,n){void 0===n&&(n=null),"children"in t&&t.children.forEach((function(n){return e(n,t)})),"anchor"in t&&t.anchor&&e(t.anchor,t),"tag"in t&&t.tag&&e(t.tag,t),"leadingComments"in t&&t.leadingComments.forEach((function(n){return e(n,t)})),"middleComments"in t&&t.middleComments.forEach((function(n){return e(n,t)})),"indicatorComment"in t&&t.indicatorComment&&e(t.indicatorComment,t),"trailingComment"in t&&t.trailingComment&&e(t.trailingComment,t),"endComments"in t&&t.endComments.forEach((function(n){return e(n,t)})),Object.defineProperty(t,"_parent",{value:n,enumerable:!1})}}));a(Lt);Lt.defineParents;var Tt=o((function(e,t){t.__esModule=!0,t.getPointText=function(e){return e.line+":"+e.column}}));a(Tt);Tt.getPointText;var Pt=o((function(e,t){function n(e,t){if(t.position.end.offset<e.position.end.offset)return!1;switch(e.type){case"sequenceItem":return t.position.start.column>e.position.start.column;case"mappingKey":case"mappingValue":return t.position.start.column>e._parent.position.start.column&&(0===e.children.length||1===e.children.length&&"blockFolded"!==e.children[0].type&&"blockLiteral"!==e.children[0].type&&("mappingValue"===e.type||e.position.start.offset!==e.children[0].position.start.offset));default:return!1}}t.__esModule=!0,t.attachComments=function(e){Lt.defineParents(e);var t=function(e){for(var t=Array.from(new Array(e.position.end.line),(function(){return{}})),n=0,r=e.comments;n<r.length;n++){var a=r[n];t[a.position.start.line-1].comment=a}return function e(t,n){if(n.position.start.offset===n.position.end.offset)return;if("leadingComments"in n){var r=n.position.start,a=t[r.line-1].leadingAttachableNode;(!a||r.column<a.position.start.column)&&(t[r.line-1].leadingAttachableNode=n)}if("trailingComment"in n&&n.position.end.column>1&&"document"!==n.type&&"documentHead"!==n.type){var o=n.position.end,i=t[o.line-1].trailingAttachableNode;(!i||o.column>=i.position.end.column)&&(t[o.line-1].trailingAttachableNode=n)}if("root"!==n.type&&"document"!==n.type&&"documentHead"!==n.type&&"documentBody"!==n.type)for(var s=n.position,u=(r=s.start,[(o=s.end).line].concat(r.line===o.line?[]:r.line)),f=0,l=u;f<l.length;f++){var c=l[f],d=t[c-1].trailingNode;(!d||o.column>=d.position.end.column)&&(t[c-1].trailingNode=n)}"children"in n&&n.children.forEach((function(n){e(t,n)}))}(t,e),t}(e),r=e.children.slice();e.comments.sort((function(e,t){return e.position.start.offset-t.position.end.offset})).filter((function(e){return!e._parent})).forEach((function(e){for(;r.length>1&&e.position.start.line>r[0].position.end.line;)r.shift();!function(e,t,r){var a=e.position.start.line,o=t[a-1].trailingAttachableNode;if(o){if(o.trailingComment)throw new Error("Unexpected multiple trailing comment at "+Tt.getPointText(e.position.start));return Lt.defineParents(e,o),void(o.trailingComment=e)}for(var i=a;i>=r.position.start.line;i--){var s=t[i-1].trailingNode,u=void 0;if(s)u=s;else{if(i===a||!t[i-1].comment)continue;u=t[i-1].comment._parent}for(;;){if(n(u,e))return Lt.defineParents(e,u),void u.endComments.push(e);if(!u._parent)break;u=u._parent}break}for(i=a+1;i<=r.position.end.line;i++){var f=t[i-1].leadingAttachableNode;if(f)return Lt.defineParents(e,f),void f.leadingComments.push(e)}var l=r.children[1];Lt.defineParents(e,l),l.endComments.push(e)}(e,t,r[0])}))}}));a(Pt);Pt.attachComments;var Ct=o((function(e,t){t.__esModule=!0,t.createNode=function(e,t){return{type:e,position:t}}}));a(Ct);Ct.createNode;var Nt,xt=(Nt=k)&&Nt.default||Nt,Rt=o((function(e,t){t.__esModule=!0,t.createRoot=function(e,t,n){return xt.__assign(xt.__assign({},Ct.createNode("root",e)),{children:t,comments:n})}}));a(Rt);Rt.createRoot;var It=o((function(e,t){t.__esModule=!0,t.removeCstBlankLine=function e(t){switch(t.type){case"DOCUMENT":for(var n=t.contents.length-1;n>=0;n--)"BLANK_LINE"===t.contents[n].type?t.contents.splice(n,1):e(t.contents[n]);for(n=t.directives.length-1;n>=0;n--)"BLANK_LINE"===t.directives[n].type&&t.directives.splice(n,1);break;case"FLOW_MAP":case"FLOW_SEQ":case"MAP":case"SEQ":for(n=t.items.length-1;n>=0;n--){var r=t.items[n];"char"in r||("BLANK_LINE"===r.type?t.items.splice(n,1):e(r))}break;case"MAP_KEY":case"MAP_VALUE":case"SEQ_ITEM":t.node&&e(t.node);break;case"ALIAS":case"BLANK_LINE":case"BLOCK_FOLDED":case"BLOCK_LITERAL":case"COMMENT":case"DIRECTIVE":case"PLAIN":case"QUOTE_DOUBLE":case"QUOTE_SINGLE":break;default:throw new Error("Unexpected node type "+JSON.stringify(t.type))}}}));a(It);It.removeCstBlankLine;var Bt=o((function(e,t){t.__esModule=!0,t.createLeadingCommentAttachable=function(){return{leadingComments:[]}}}));a(Bt);Bt.createLeadingCommentAttachable;var Dt=o((function(e,t){t.__esModule=!0,t.createTrailingCommentAttachable=function(e){return void 0===e&&(e=null),{trailingComment:e}}}));a(Dt);Dt.createTrailingCommentAttachable;var jt=o((function(e,t){t.__esModule=!0,t.createCommentAttachable=function(){return xt.__assign(xt.__assign({},Bt.createLeadingCommentAttachable()),Dt.createTrailingCommentAttachable())}}));a(jt);jt.createCommentAttachable;var Yt=o((function(e,t){t.__esModule=!0,t.createAlias=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("alias",e)),jt.createCommentAttachable()),t),{value:n})}}));a(Yt);Yt.createAlias;var Ft=o((function(e,t){t.__esModule=!0,t.transformAlias=function(e,t){var n=e.cstNode;return Yt.createAlias(t.transformRange({origStart:n.valueRange.origStart-1,origEnd:n.valueRange.origEnd}),t.transformContent(e),n.rawValue)}}));a(Ft);Ft.transformAlias;var Ut=o((function(e,t){t.__esModule=!0,t.createBlockFolded=function(e){return xt.__assign(xt.__assign({},e),{type:"blockFolded"})}}));a(Ut);Ut.createBlockFolded;var Kt=o((function(e,t){t.__esModule=!0,t.createBlockValue=function(e,t,n,r,a,o){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("blockValue",e)),Bt.createLeadingCommentAttachable()),t),{chomping:n,indent:r,value:a,indicatorComment:o})}}));a(Kt);Kt.createBlockValue;var Wt=o((function(e,t){t.__esModule=!0,function(e){e.Tag="!",e.Anchor="&",e.Comment="#"}(t.PropLeadingCharacter||(t.PropLeadingCharacter={}))}));a(Wt);Wt.PropLeadingCharacter;var Qt=o((function(e,t){t.__esModule=!0,t.createAnchor=function(e,t){return xt.__assign(xt.__assign({},Ct.createNode("anchor",e)),{value:t})}}));a(Qt);Qt.createAnchor;var Vt=o((function(e,t){t.__esModule=!0,t.createComment=function(e,t){return xt.__assign(xt.__assign({},Ct.createNode("comment",e)),{value:t})}}));a(Vt);Vt.createComment;var $t=o((function(e,t){t.__esModule=!0,t.createContent=function(e,t,n){return{anchor:t,tag:e,middleComments:n}}}));a($t);$t.createContent;var qt=o((function(e,t){t.__esModule=!0,t.createTag=function(e,t){return xt.__assign(xt.__assign({},Ct.createNode("tag",e)),{value:t})}}));a(qt);qt.createTag;var Jt=o((function(e,t){t.__esModule=!0,t.transformContent=function(e,t,n){void 0===n&&(n=function(){return!1});for(var r=e.cstNode,a=[],o=null,i=null,s=null,u=0,f=r.props;u<f.length;u++){var l=f[u],c=t.text[l.origStart];switch(c){case Wt.PropLeadingCharacter.Tag:o=o||l,i=qt.createTag(t.transformRange(l),e.tag);break;case Wt.PropLeadingCharacter.Anchor:o=o||l,s=Qt.createAnchor(t.transformRange(l),r.anchor);break;case Wt.PropLeadingCharacter.Comment:var d=Vt.createComment(t.transformRange(l),t.text.slice(l.origStart+1,l.origEnd));t.comments.push(d),!n(d)&&o&&o.origEnd<=l.origStart&&l.origEnd<=r.valueRange.origStart&&a.push(d);break;default:throw new Error("Unexpected leading character "+JSON.stringify(c))}}return $t.createContent(i,s,a)}}));a(Jt);Jt.transformContent;var Gt=o((function(e,t){var n;t.__esModule=!0,function(e){e.CLIP="clip",e.STRIP="strip",e.KEEP="keep"}(n||(n={})),t.transformAstBlockValue=function(e,t){var r=e.cstNode,a="CLIP"===r.chomping?0:1,o=r.header.origEnd-r.header.origStart-1-a!=0,i=t.transformRange({origStart:r.header.origStart,origEnd:r.valueRange.origEnd}),s=null,u=Jt.transformContent(e,t,(function(e){if(!(i.start.offset<e.position.start.offset&&e.position.end.offset<i.end.offset))return!1;if(s)throw new Error("Unexpected multiple indicator comments at "+Tt.getPointText(e.position.start));return s=e,!0}));return Kt.createBlockValue(i,u,n[r.chomping],o?r.blockIndent:null,r.strValue,s)}}));a(Gt);Gt.transformAstBlockValue;var Ht=o((function(e,t){t.__esModule=!0,t.transformBlockFolded=function(e,t){return Ut.createBlockFolded(Gt.transformAstBlockValue(e,t))}}));a(Ht);Ht.transformBlockFolded;var zt=o((function(e,t){t.__esModule=!0,t.createBlockLiteral=function(e){return xt.__assign(xt.__assign({},e),{type:"blockLiteral"})}}));a(zt);zt.createBlockLiteral;var Zt=o((function(e,t){t.__esModule=!0,t.transformBlockLiteral=function(e,t){return zt.createBlockLiteral(Gt.transformAstBlockValue(e,t))}}));a(Zt);Zt.transformBlockLiteral;var Xt=o((function(e,t){t.__esModule=!0,t.transformComment=function(e,t){return Vt.createComment(t.transformRange(e.range),e.comment)}}));a(Xt);Xt.transformComment;var en=o((function(e,t){t.__esModule=!0,t.createDirective=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("directive",e)),jt.createCommentAttachable()),{name:t,parameters:n})}}));a(en);en.createDirective;var tn=o((function(e,t){t.__esModule=!0,t.extractPropComments=function(e,t){for(var n=0,r=e.props;n<r.length;n++){var a=r[n],o=t.text[a.origStart];switch(o){case Wt.PropLeadingCharacter.Comment:t.comments.push(Vt.createComment(t.transformRange(a),t.text.slice(a.origStart+1,a.origEnd)));break;default:throw new Error("Unexpected leading character "+JSON.stringify(o))}}}}));a(tn);tn.extractPropComments;var nn=o((function(e,t){t.__esModule=!0,t.transformDirective=function(e,t){return tn.extractPropComments(e,t),en.createDirective(t.transformRange(e.range),e.name,e.parameters)}}));a(nn);nn.transformDirective;var rn=o((function(e,t){t.__esModule=!0,t.createDocument=function(e,t,n,r){return xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("document",e)),Dt.createTrailingCommentAttachable(r)),{children:[t,n]})}}));a(rn);rn.createDocument;var an=o((function(e,t){t.__esModule=!0,t.createPosition=function(e,t){return{start:e,end:t}},t.createEmptyPosition=function(e){return{start:e,end:e}}}));a(an);an.createPosition,an.createEmptyPosition;var on=o((function(e,t){t.__esModule=!0,t.createEndCommentAttachable=function(e){return void 0===e&&(e=[]),{endComments:e}}}));a(on);on.createEndCommentAttachable;var sn=o((function(e,t){t.__esModule=!0,t.createDocumentBody=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("documentBody",e)),on.createEndCommentAttachable(n)),{children:t?[t]:[]})}}));a(sn);sn.createDocumentBody;var un=o((function(e,t){t.__esModule=!0,t.getLast=function(e){return e[e.length-1]}}));a(un);un.getLast;var fn=o((function(e,t){t.__esModule=!0,t.getMatchIndex=function(e,t){var n=e.match(t);return n?n.index:-1}}));a(fn);fn.getMatchIndex;var ln=o((function(e,t){t.__esModule=!0,t.transformDocumentBody=function(e,t,n){var r,a=e.cstNode,o=function(e,t,n){for(var r=[],a=[],o=[],i=!1,s=e.contents.length-1;s>=0;s--){var u=e.contents[s];if("COMMENT"===u.type){var f=t.transformNode(u);n&&n.line===f.position.start.line?o.unshift(f):i?r.unshift(f):f.position.start.offset>=e.valueRange.origEnd?a.unshift(f):r.unshift(f)}else i=!0}if(a.length>1)throw new Error("Unexpected multiple document trailing comments at "+Tt.getPointText(a[1].position.start));if(o.length>1)throw new Error("Unexpected multiple documentHead trailing comments at "+Tt.getPointText(o[1].position.start));return{comments:r,endComments:[],documentTrailingComment:un.getLast(a)||null,documentHeadTrailingComment:un.getLast(o)||null}}(a,t,n),i=o.comments,s=o.endComments,u=o.documentTrailingComment,f=o.documentHeadTrailingComment,l=t.transformNode(e.contents),c=function(e,t,n){var r=fn.getMatchIndex(n.text.slice(e.valueRange.origEnd),/^\.\.\./),a=-1===r?e.valueRange.origEnd:Math.max(0,e.valueRange.origEnd-1);"\r"===n.text[a-1]&&a--;var o=n.transformRange({origStart:null!==t?t.position.start.offset:a,origEnd:a}),i=-1===r?o.end:n.transformOffset(e.valueRange.origEnd+3);return{position:o,documentEndPoint:i}}(a,l,t),d=c.position,h=c.documentEndPoint;return(r=t.comments).push.apply(r,xt.__spreadArrays(i,s)),{documentBody:sn.createDocumentBody(d,l,s),documentEndPoint:h,documentTrailingComment:u,documentHeadTrailingComment:f}}}));a(ln);ln.transformDocumentBody;var cn=o((function(e,t){t.__esModule=!0,t.createDocumentHead=function(e,t,n,r){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("documentHead",e)),on.createEndCommentAttachable(n)),Dt.createTrailingCommentAttachable(r)),{children:t})}}));a(cn);cn.createDocumentHead;var dn=o((function(e,t){t.__esModule=!0,t.transformDocumentHead=function(e,t){var n,r=e.cstNode,a=function(e,t){for(var n=[],r=[],a=[],o=!1,i=e.directives.length-1;i>=0;i--){var s=t.transformNode(e.directives[i]);"comment"===s.type?o?r.unshift(s):a.unshift(s):(o=!0,n.unshift(s))}return{directives:n,comments:r,endComments:a}}(r,t),o=a.directives,i=a.comments,s=a.endComments,u=function(e,t,n){var r=fn.getMatchIndex(n.text.slice(0,e.valueRange.origStart),/---\s*$/),a=-1===r?{origStart:e.valueRange.origStart,origEnd:e.valueRange.origStart}:{origStart:r,origEnd:r+3};0!==t.length&&(a.origStart=t[0].position.start.offset);return{position:n.transformRange(a),endMarkerPoint:-1===r?null:n.transformOffset(r)}}(r,o,t),f=u.position,l=u.endMarkerPoint;return(n=t.comments).push.apply(n,xt.__spreadArrays(i,s)),{createDocumentHeadWithTrailingComment:function(e){return e&&t.comments.push(e),cn.createDocumentHead(f,o,s,e)},documentHeadEndMarkerPoint:l}}}));a(dn);dn.transformDocumentHead;var hn=o((function(e,t){t.__esModule=!0,t.transformDocument=function(e,t){var n=dn.transformDocumentHead(e,t),r=n.createDocumentHeadWithTrailingComment,a=n.documentHeadEndMarkerPoint,o=ln.transformDocumentBody(e,t,a),i=o.documentBody,s=o.documentEndPoint,u=o.documentTrailingComment,f=r(o.documentHeadTrailingComment);return u&&t.comments.push(u),rn.createDocument(an.createPosition(f.position.start,s),f,i,u)}}));a(hn);hn.transformDocument;var pn=o((function(e,t){t.__esModule=!0,t.createFlowCollection=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("flowCollection",e)),jt.createCommentAttachable()),t),{children:n})}}));a(pn);pn.createFlowCollection;var vn=o((function(e,t){t.__esModule=!0,t.createFlowMapping=function(e,t,n){return xt.__assign(xt.__assign({},pn.createFlowCollection(e,t,n)),{type:"flowMapping"})}}));a(vn);vn.createFlowMapping;var gn=o((function(e,t){t.__esModule=!0,t.createFlowMappingItem=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("flowMappingItem",e)),Bt.createLeadingCommentAttachable()),{children:[t,n]})}}));a(gn);gn.createFlowMappingItem;var mn=o((function(e,t){t.__esModule=!0,t.extractComments=function(e,t){for(var n=[],r=0,a=e;r<a.length;r++){var o=a[r];o&&"type"in o&&"COMMENT"===o.type?t.comments.push(t.transformNode(o)):n.push(o)}return n}}));a(mn);mn.extractComments;var yn=o((function(e,t){t.__esModule=!0,t.getFlowMapItemAdditionalRanges=function(e){var t=["?",":"].map((function(t){var n=e.find((function(e){return"char"in e&&e.char===t}));return n?{origStart:n.origOffset,origEnd:n.origOffset+1}:null}));return{additionalKeyRange:t[0],additionalValueRange:t[1]}}}));a(yn);yn.getFlowMapItemAdditionalRanges;var _n=o((function(e,t){t.__esModule=!0,t.createSlicer=function(e,t){var n=t;return function(t){return e.slice(n,n=t)}}}));a(_n);_n.createSlicer;var bn=o((function(e,t){t.__esModule=!0,t.groupCstFlowCollectionItems=function(e){for(var t=[],n=_n.createSlicer(e,1),r=!1,a=1;a<e.length-1;a++){var o=e[a];"char"in o&&","===o.char?(t.push(n(a)),n(a+1),r=!1):r=!0}return r&&t.push(n(e.length-1)),t}}));a(bn);bn.groupCstFlowCollectionItems;var wn=o((function(e,t){t.__esModule=!0,t.createMappingKey=function(e,t){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("mappingKey",e)),Dt.createTrailingCommentAttachable()),on.createEndCommentAttachable()),{children:t?[t]:[]})}}));a(wn);wn.createMappingKey;var En=o((function(e,t){t.__esModule=!0,t.createMappingValue=function(e,t){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("mappingValue",e)),jt.createCommentAttachable()),on.createEndCommentAttachable()),{children:t?[t]:[]})}}));a(En);En.createMappingValue;var On=o((function(e,t){t.__esModule=!0,t.transformAstPair=function(e,t,n,r,a){var o=t.transformNode(e.key),i=t.transformNode("MERGE_PAIR"===e.type?e.value.type?e.value:e.value.items[0]:e.value),s=o||r?wn.createMappingKey(t.transformRange({origStart:r?r.origStart:o.position.start.offset,origEnd:o?o.position.end.offset:r.origStart+1}),o):null,u=i||a?En.createMappingValue(t.transformRange({origStart:a?a.origStart:i.position.start.offset,origEnd:i?i.position.end.offset:a.origStart+1}),i):null;return n(an.createPosition(s?s.position.start:u.position.start,u?u.position.end:s.position.end),s||wn.createMappingKey(an.createEmptyPosition(u.position.start),null),u||En.createMappingValue(an.createEmptyPosition(s.position.end),null))}}));a(On);On.transformAstPair;var Mn=o((function(e,t){t.__esModule=!0,t.transformFlowMap=function(e,t){var n=mn.extractComments(e.cstNode.items,t),r=bn.groupCstFlowCollectionItems(n),a=e.items.map((function(e,n){var a=r[n],o=yn.getFlowMapItemAdditionalRanges(a),i=o.additionalKeyRange,s=o.additionalValueRange;return On.transformAstPair(e,t,gn.createFlowMappingItem,i,s)})),o=n[0],i=un.getLast(n);return vn.createFlowMapping(t.transformRange({origStart:o.origOffset,origEnd:i.origOffset+1}),t.transformContent(e),a)}}));a(Mn);Mn.transformFlowMap;var Sn=o((function(e,t){t.__esModule=!0,t.createFlowSequence=function(e,t,n){return xt.__assign(xt.__assign({},pn.createFlowCollection(e,t,n)),{type:"flowSequence"})}}));a(Sn);Sn.createFlowSequence;var An=o((function(e,t){t.__esModule=!0,t.createFlowSequenceItem=function(e,t){return xt.__assign(xt.__assign({},Ct.createNode("flowSequenceItem",e)),{children:[t]})}}));a(An);An.createFlowSequenceItem;var kn=o((function(e,t){t.__esModule=!0,t.transformFlowSeq=function(e,t){var n=mn.extractComments(e.cstNode.items,t),r=bn.groupCstFlowCollectionItems(n),a=e.items.map((function(e,n){if("PAIR"!==e.type){var a=t.transformNode(e);return An.createFlowSequenceItem(an.createPosition(a.position.start,a.position.end),a)}var o=r[n],i=yn.getFlowMapItemAdditionalRanges(o),s=i.additionalKeyRange,u=i.additionalValueRange;return On.transformAstPair(e,t,gn.createFlowMappingItem,s,u)})),o=n[0],i=un.getLast(n);return Sn.createFlowSequence(t.transformRange({origStart:o.origOffset,origEnd:i.origOffset+1}),t.transformContent(e),a)}}));a(kn);kn.transformFlowSeq;var Ln=o((function(e,t){t.__esModule=!0,t.createMapping=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("mapping",e)),Bt.createLeadingCommentAttachable()),t),{children:n})}}));a(Ln);Ln.createMapping;var Tn=o((function(e,t){t.__esModule=!0,t.createMappingItem=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("mappingItem",e)),Bt.createLeadingCommentAttachable()),{children:[t,n]})}}));a(Tn);Tn.createMappingItem;var Pn=o((function(e,t){t.__esModule=!0,t.transformMap=function(e,t){var n=e.cstNode;n.items.filter((function(e){return"MAP_KEY"===e.type||"MAP_VALUE"===e.type})).forEach((function(e){return tn.extractPropComments(e,t)}));var r=function(e){for(var t=[],n=_n.createSlicer(e,0),r=!1,a=0;a<e.length;a++){"MAP_VALUE"!==e[a].type?(r&&t.push(n(a)),r=!0):(t.push(n(a+1)),r=!1)}r&&t.push(n(1/0));return t}(mn.extractComments(n.items,t)),a=e.items.map((function(e,n){var a=r[n],o="MAP_VALUE"===a[0].type?[null,a[0].range]:[a[0].range,1===a.length?null:a[1].range],i=o[0],s=o[1];return On.transformAstPair(e,t,Tn.createMappingItem,i,s)}));return Ln.createMapping(an.createPosition(a[0].position.start,un.getLast(a).position.end),t.transformContent(e),a)}}));a(Pn);Pn.transformMap;var Cn=o((function(e,t){t.__esModule=!0,t.createPlain=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("plain",e)),jt.createCommentAttachable()),t),{value:n})}}));a(Cn);Cn.createPlain;var Nn=o((function(e,t){t.__esModule=!0,t.findLastCharIndex=function(e,t,n){for(var r=t;r>=0;r--)if(n.test(e[r]))return r;return-1}}));a(Nn);Nn.findLastCharIndex;var xn=o((function(e,t){t.__esModule=!0,t.transformPlain=function(e,t){var n=e.cstNode;return Cn.createPlain(t.transformRange({origStart:n.valueRange.origStart,origEnd:Nn.findLastCharIndex(t.text,n.valueRange.origEnd-1,/\S/)+1}),t.transformContent(e),n.strValue)}}));a(xn);xn.transformPlain;var Rn=o((function(e,t){t.__esModule=!0,t.createQuoteDouble=function(e){return xt.__assign(xt.__assign({},e),{type:"quoteDouble"})}}));a(Rn);Rn.createQuoteDouble;var In=o((function(e,t){t.__esModule=!0,t.createQuoteValue=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("quoteValue",e)),t),jt.createCommentAttachable()),{value:n})}}));a(In);In.createQuoteValue;var Bn=o((function(e,t){t.__esModule=!0,t.transformAstQuoteValue=function(e,t){var n=e.cstNode;return In.createQuoteValue(t.transformRange(n.valueRange),t.transformContent(e),n.strValue)}}));a(Bn);Bn.transformAstQuoteValue;var Dn=o((function(e,t){t.__esModule=!0,t.transformQuoteDouble=function(e,t){return Rn.createQuoteDouble(Bn.transformAstQuoteValue(e,t))}}));a(Dn);Dn.transformQuoteDouble;var jn=o((function(e,t){t.__esModule=!0,t.createQuoteSingle=function(e){return xt.__assign(xt.__assign({},e),{type:"quoteSingle"})}}));a(jn);jn.createQuoteSingle;var Yn=o((function(e,t){t.__esModule=!0,t.transformQuoteSingle=function(e,t){return jn.createQuoteSingle(Bn.transformAstQuoteValue(e,t))}}));a(Yn);Yn.transformQuoteSingle;var Fn=o((function(e,t){t.__esModule=!0,t.createSequence=function(e,t,n){return xt.__assign(xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("sequence",e)),Bt.createLeadingCommentAttachable()),on.createEndCommentAttachable()),t),{children:n})}}));a(Fn);Fn.createSequence;var Un=o((function(e,t){t.__esModule=!0,t.createSequenceItem=function(e,t){return xt.__assign(xt.__assign(xt.__assign(xt.__assign({},Ct.createNode("sequenceItem",e)),jt.createCommentAttachable()),on.createEndCommentAttachable()),{children:t?[t]:[]})}}));a(Un);Un.createSequenceItem;var Kn=o((function(e,t){t.__esModule=!0,t.transformSeq=function(e,t){var n=mn.extractComments(e.cstNode.items,t).map((function(n,r){tn.extractPropComments(n,t);var a=t.transformNode(e.items[r]);return Un.createSequenceItem(an.createPosition(t.transformOffset(n.valueRange.origStart),null===a?t.transformOffset(n.valueRange.origStart+1):a.position.end),a)}));return Fn.createSequence(an.createPosition(n[0].position.start,un.getLast(n).position.end),t.transformContent(e),n)}}));a(Kn);Kn.transformSeq;var Wn=o((function(e,t){t.__esModule=!0,t.transformNode=function(e,t){if(null===e)return null;switch(e.type){case"ALIAS":return Ft.transformAlias(e,t);case"BLOCK_FOLDED":return Ht.transformBlockFolded(e,t);case"BLOCK_LITERAL":return Zt.transformBlockLiteral(e,t);case"COMMENT":return Xt.transformComment(e,t);case"DIRECTIVE":return nn.transformDirective(e,t);case"DOCUMENT":return hn.transformDocument(e,t);case"FLOW_MAP":return Mn.transformFlowMap(e,t);case"FLOW_SEQ":return kn.transformFlowSeq(e,t);case"MAP":return Pn.transformMap(e,t);case"PLAIN":return xn.transformPlain(e,t);case"QUOTE_DOUBLE":return Dn.transformQuoteDouble(e,t);case"QUOTE_SINGLE":return Yn.transformQuoteSingle(e,t);case"SEQ":return Kn.transformSeq(e,t);default:throw new Error("Unexpected node type "+e.type)}}}));a(Wn);Wn.transformNode;var Qn=o((function(e,t){t.__esModule=!0,t.createError=function(e,t,n){var r=new SyntaxError(e);return r.name="YAMLSyntaxError",r.source=t,r.position=n,r}}));a(Qn);Qn.createError;var Vn=o((function(e,t){t.__esModule=!0,t.transformError=function(e,t){var n=e.source.range||e.source.valueRange;return Qn.createError(e.message,t.text,t.transformRange(n))}}));a(Vn);Vn.transformError;var $n=o((function(e,t){t.__esModule=!0,t.createPoint=function(e,t,n){return{offset:e,line:t,column:n}}}));a($n);$n.createPoint;var qn=o((function(e,t){t.__esModule=!0,t.transformOffset=function(e,t){e<0?e=0:e>t.text.length&&(e=t.text.length);var n=t.locator.locationForIndex(e);return $n.createPoint(e,n.line+1,n.column+1)}}));a(qn);qn.transformOffset;var Jn=o((function(e,t){t.__esModule=!0,t.transformRange=function(e,t){return an.createPosition(t.transformOffset(e.origStart),t.transformOffset(e.origEnd))}}));a(Jn);Jn.transformRange;var Gn=o((function(e,t){t.__esModule=!0;var n=!0;t.addOrigRange=function(e){if(!e.setOrigRanges()){var t=function(e){return function(e){return"number"==typeof e.start}(e)?(e.origStart=e.start,e.origEnd=e.end,n):function(e){return"number"==typeof e.offset}(e)?(e.origOffset=e.offset,n):void 0};e.forEach((function(e){return function e(t,r){if(!t||"object"!==i(t))return;if(r(t)===n)return;for(var a=0,o=Object.keys(t);a<o.length;a++){var s=o[a];if("context"!==s&&"error"!==s){var u=t[s];Array.isArray(u)?u.forEach((function(t){return e(t,r)})):e(u,r)}}}(e,t)}))}}}));a(Gn);Gn.addOrigRange;var Hn=o((function(e,t){t.__esModule=!0,t.removeFakeNodes=function e(t){if("children"in t){if(1===t.children.length){var n=t.children[0];if("plain"===n.type&&null===n.tag&&null===n.anchor&&""===n.value)return t.children.splice(0,1),t}t.children.forEach(e)}return t}}));a(Hn);Hn.removeFakeNodes;var zn=o((function(e,t){t.__esModule=!0,t.createUpdater=function(e,t,n,r){var a=t(e);return function(t){r(a,t)&&n(e,a=t)}}}));a(zn);zn.createUpdater;var Zn=o((function(e,t){function n(e){return e.start}function r(e,t){e.start=t}function a(e){return e.end}function o(e,t){e.end=t}function i(e,t){return t.offset<e.offset}function s(e,t){return t.offset>e.offset}t.__esModule=!0,t.updatePositions=function e(t){if(null!==t&&"children"in t){var u=t.children;if(u.forEach(e),"document"===t.type){var f=t.children,l=f[0],c=f[1];l.position.start.offset===l.position.end.offset?l.position.start=l.position.end=c.position.start:c.position.start.offset===c.position.end.offset&&(c.position.start=c.position.end=l.position.end)}var d=zn.createUpdater(t.position,n,r,i),h=zn.createUpdater(t.position,a,o,s);"endComments"in t&&0!==t.endComments.length&&(d(t.endComments[0].position.start),h(un.getLast(t.endComments).position.end));var p=u.filter((function(e){return null!==e}));if(0!==p.length){var v=p[0],g=un.getLast(p);d(v.position.start),h(g.position.end),"leadingComments"in v&&0!==v.leadingComments.length&&d(v.leadingComments[0].position.start),"tag"in v&&v.tag&&d(v.tag.position.start),"anchor"in v&&v.anchor&&d(v.anchor.position.start),"trailingComment"in g&&g.trailingComment&&h(g.trailingComment.position.end)}}}}));a(Zn);Zn.updatePositions;var Xn=o((function(e,t){t.__esModule=!0,t.parse=function(e){var t=kt.parseCST(e);Gn.addOrigRange(t);var n=t.map((function(e){return new kt.Document({merge:!0,keepCstNodes:!0}).parse(e)})),r=[],a={text:e,locator:new L.default(e),comments:r,transformOffset:function(e){return qn.transformOffset(e,a)},transformRange:function(e){return Jn.transformRange(e,a)},transformNode:function(e){return Wn.transformNode(e,a)},transformContent:function(e){return Jt.transformContent(e,a)}},o=n.find((function(e){return 0!==e.errors.length}));if(o)throw Vn.transformError(o.errors[0],a);n.forEach((function(e){return It.removeCstBlankLine(e.cstNode)}));var i=Rt.createRoot(a.transformRange({origStart:0,origEnd:a.text.length}),n.map(a.transformNode),r);return Pt.attachComments(i),Zn.updatePositions(i),Hn.removeFakeNodes(i),i}}));a(Xn);Xn.parse;var er=o((function(e,t){t.__esModule=!0,xt.__exportStar(Xn,t)}));a(er);var tr={parsers:{yaml:{astFormat:"yaml",parse:function(e){try{var n=er.parse(e);return delete n.comments,n}catch(e){throw e&&e.position?t(e.message,e.position):e}},hasPragma:n,locStart:function(e){return e.position.start.offset},locEnd:function(e){return e.position.end.offset}}}},nr=tr.parsers;e.default=tr,e.parsers=nr,Object.defineProperty(e,"__esModule",{value:!0})}));
