!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(((e=e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.angular={}))}(this,(function(e){"use strict";var t=function(e){return e.length>0?e[e.length-1]:null};var n={locStart:function e(t,n){return!(n=n||{}).ignoreDecorators&&t.declaration&&t.declaration.decorators&&t.declaration.decorators.length>0?e(t.declaration.decorators[0]):!n.ignoreDecorators&&t.decorators&&t.decorators.length>0?e(t.decorators[0]):t.__location?t.__location.startOffset:t.range?t.range[0]:"number"==typeof t.start?t.start:t.loc?t.loc.start:null},locEnd:function e(n){var i=n.nodes&&t(n.nodes);if(i&&n.source&&!n.source.end&&(n=i),n.__location)return n.__location.endOffset;var r=n.range?n.range[1]:"number"==typeof n.end?n.end:null;return n.typeAnnotation?Math.max(r,e(n.typeAnnotation)):n.loc&&!r?n.loc.end:r}};function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e,t){return e(t={exports:{}},t.exports),t.exports}function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function u(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&h(e,t)}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){return(h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(!(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e)))return;var n=[],i=!0,r=!1,s=void 0;try{for(var a,o=e[Symbol.iterator]();!(i=(a=o.next()).done)&&(n.push(a.value),!t||n.length!==t);i=!0);}catch(e){r=!0,s=e}finally{try{i||null==o.return||o.return()}finally{if(r)throw s}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var f=r((function(e,t){var n="\n",i="\r",r=function(){function e(e){this.string=e;for(var t=[0],r=0;r<e.length;)switch(e[r]){case n:r+=n.length,t.push(r);break;case i:e[r+=i.length]===n&&(r+=n.length),t.push(r);break;default:r++}this.offsets=t}return e.prototype.locationForIndex=function(e){if(e<0||e>this.string.length)return null;for(var t=0,n=this.offsets;n[t+1]<=e;)t++;return{line:t,column:e-n[t]}},e.prototype.indexForLocation=function(e){var t=e.line,n=e.column;return t<0||t>=this.offsets.length?null:n<0||n>this.lengthOfLine(t)?null:this.offsets[t]+n},e.prototype.lengthOfLine=function(e){var t=this.offsets[e];return(e===this.offsets.length-1?this.string.length:this.offsets[e+1])-t},e}();t.__esModule=!0,t.default=r}));i(f);var d=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.Context=function e(t){a(this,e),this.text=t,this.locator=new n(this.text)};var n=function(){function e(t){a(this,e),this._lineAndColumn=new f.default(t)}return u(e,[{key:"locationForIndex",value:function(e){var t=this._lineAndColumn.locationForIndex(e);return{line:t.line+1,column:t.column}}}]),e}()}));i(d);d.Context;
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */var y=function e(t,n,i,r){a(this,e),this.input=n,this.errLocation=i,this.ctxLocation=r,this.message="Parser Error: ".concat(t," ").concat(i," [").concat(n,"] in ").concat(r)},x=function e(t,n){a(this,e),this.start=t,this.end=n},g=function(){function e(t){a(this,e),this.span=t}return u(e,[{key:"visit",value:function(e){return null}},{key:"toString",value:function(){return"AST"}}]),e}(),k=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).prefix=n,s.uninterpretedExpression=i,s.location=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitQuote(this,t)}},{key:"toString",value:function(){return"Quote"}}]),t}(g),m=function(e){function t(){return a(this,t),p(this,c(t).apply(this,arguments))}return l(t,e),u(t,[{key:"visit",value:function(e){}}]),t}(g),w=function(e){function t(){return a(this,t),p(this,c(t).apply(this,arguments))}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitImplicitReceiver(this,t)}}]),t}(g),C=function(e){function t(e,n){var i;return a(this,t),(i=p(this,c(t).call(this,e))).expressions=n,i}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitChain(this,t)}}]),t}(g),P=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).condition=n,s.trueExp=i,s.falseExp=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitConditional(this,t)}}]),t}(g),b=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).receiver=n,r.name=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitPropertyRead(this,t)}}]),t}(g),A=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).receiver=n,s.name=i,s.value=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitPropertyWrite(this,t)}}]),t}(g),E=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).receiver=n,r.name=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitSafePropertyRead(this,t)}}]),t}(g),S=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).obj=n,r.key=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitKeyedRead(this,t)}}]),t}(g),N=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).obj=n,s.key=i,s.value=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitKeyedWrite(this,t)}}]),t}(g),O=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).exp=n,s.name=i,s.args=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitPipe(this,t)}}]),t}(g),I=function(e){function t(e,n){var i;return a(this,t),(i=p(this,c(t).call(this,e))).value=n,i}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitLiteralPrimitive(this,t)}}]),t}(g),_=function(e){function t(e,n){var i;return a(this,t),(i=p(this,c(t).call(this,e))).expressions=n,i}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitLiteralArray(this,t)}}]),t}(g),L=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).keys=n,r.values=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitLiteralMap(this,t)}}]),t}(g),M=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).strings=n,r.expressions=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitInterpolation(this,t)}}]),t}(g),K=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).operation=n,s.left=i,s.right=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitBinary(this,t)}}]),t}(g),B=function(e){function t(e,n){var i;return a(this,t),(i=p(this,c(t).call(this,e))).expression=n,i}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitPrefixNot(this,t)}}]),t}(g),T=function(e){function t(e,n){var i;return a(this,t),(i=p(this,c(t).call(this,e))).expression=n,i}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitNonNullAssert(this,t)}}]),t}(g),R=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).receiver=n,s.name=i,s.args=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitMethodCall(this,t)}}]),t}(g),j=function(e){function t(e,n,i,r){var s;return a(this,t),(s=p(this,c(t).call(this,e))).receiver=n,s.name=i,s.args=r,s}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitSafeMethodCall(this,t)}}]),t}(g),F=function(e){function t(e,n,i){var r;return a(this,t),(r=p(this,c(t).call(this,e))).target=n,r.args=i,r}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitFunctionCall(this,t)}}]),t}(g),V=function e(t,n){a(this,e),this.start=t,this.end=n},W=function(e){function t(e,n,i,r,s){var o;return a(this,t),(o=p(this,c(t).call(this,new x(0,null==n?0:n.length)))).ast=e,o.source=n,o.location=i,o.errors=s,o.sourceSpan=new V(r,r+o.span.end),o}return l(t,e),u(t,[{key:"visit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e.visitASTWithSource?e.visitASTWithSource(this,t):this.ast.visit(e,t)}},{key:"toString",value:function(){return"".concat(this.source," in ").concat(this.location)}}]),t}(g),G=function e(t,n,i,r,s){a(this,e),this.span=t,this.key=n,this.keyIsVar=i,this.name=r,this.expression=s},U=function(){function e(){a(this,e)}return u(e,[{key:"visitBinary",value:function(e,t){}},{key:"visitChain",value:function(e,t){}},{key:"visitConditional",value:function(e,t){}},{key:"visitFunctionCall",value:function(e,t){}},{key:"visitImplicitReceiver",value:function(e,t){}},{key:"visitInterpolation",value:function(e,t){}},{key:"visitKeyedRead",value:function(e,t){}},{key:"visitKeyedWrite",value:function(e,t){}},{key:"visitLiteralArray",value:function(e,t){}},{key:"visitLiteralMap",value:function(e,t){}},{key:"visitLiteralPrimitive",value:function(e,t){}},{key:"visitMethodCall",value:function(e,t){}},{key:"visitPipe",value:function(e,t){}},{key:"visitPrefixNot",value:function(e,t){}},{key:"visitNonNullAssert",value:function(e,t){}},{key:"visitPropertyRead",value:function(e,t){}},{key:"visitPropertyWrite",value:function(e,t){}},{key:"visitQuote",value:function(e,t){}},{key:"visitSafeMethodCall",value:function(e,t){}},{key:"visitSafePropertyRead",value:function(e,t){}}]),e}(),Q=function(){function e(){a(this,e)}return u(e,[{key:"visitBinary",value:function(e,t){return e.left.visit(this,t),e.right.visit(this,t),null}},{key:"visitChain",value:function(e,t){return this.visitAll(e.expressions,t)}},{key:"visitConditional",value:function(e,t){return e.condition.visit(this,t),e.trueExp.visit(this,t),e.falseExp.visit(this,t),null}},{key:"visitPipe",value:function(e,t){return e.exp.visit(this,t),this.visitAll(e.args,t),null}},{key:"visitFunctionCall",value:function(e,t){return e.target.visit(this,t),this.visitAll(e.args,t),null}},{key:"visitImplicitReceiver",value:function(e,t){return null}},{key:"visitInterpolation",value:function(e,t){return this.visitAll(e.expressions,t)}},{key:"visitKeyedRead",value:function(e,t){return e.obj.visit(this,t),e.key.visit(this,t),null}},{key:"visitKeyedWrite",value:function(e,t){return e.obj.visit(this,t),e.key.visit(this,t),e.value.visit(this,t),null}},{key:"visitLiteralArray",value:function(e,t){return this.visitAll(e.expressions,t)}},{key:"visitLiteralMap",value:function(e,t){return this.visitAll(e.values,t)}},{key:"visitLiteralPrimitive",value:function(e,t){return null}},{key:"visitMethodCall",value:function(e,t){return e.receiver.visit(this,t),this.visitAll(e.args,t)}},{key:"visitPrefixNot",value:function(e,t){return e.expression.visit(this,t),null}},{key:"visitNonNullAssert",value:function(e,t){return e.expression.visit(this,t),null}},{key:"visitPropertyRead",value:function(e,t){return e.receiver.visit(this,t),null}},{key:"visitPropertyWrite",value:function(e,t){return e.receiver.visit(this,t),e.value.visit(this,t),null}},{key:"visitSafePropertyRead",value:function(e,t){return e.receiver.visit(this,t),null}},{key:"visitSafeMethodCall",value:function(e,t){return e.receiver.visit(this,t),this.visitAll(e.args,t)}},{key:"visitAll",value:function(e,t){var n=this;return e.forEach((function(e){return e.visit(n,t)})),null}},{key:"visitQuote",value:function(e,t){return null}}]),e}(),z=function(){function e(){a(this,e)}return u(e,[{key:"visitImplicitReceiver",value:function(e,t){return e}},{key:"visitInterpolation",value:function(e,t){return new M(e.span,e.strings,this.visitAll(e.expressions))}},{key:"visitLiteralPrimitive",value:function(e,t){return new I(e.span,e.value)}},{key:"visitPropertyRead",value:function(e,t){return new b(e.span,e.receiver.visit(this),e.name)}},{key:"visitPropertyWrite",value:function(e,t){return new A(e.span,e.receiver.visit(this),e.name,e.value.visit(this))}},{key:"visitSafePropertyRead",value:function(e,t){return new E(e.span,e.receiver.visit(this),e.name)}},{key:"visitMethodCall",value:function(e,t){return new R(e.span,e.receiver.visit(this),e.name,this.visitAll(e.args))}},{key:"visitSafeMethodCall",value:function(e,t){return new j(e.span,e.receiver.visit(this),e.name,this.visitAll(e.args))}},{key:"visitFunctionCall",value:function(e,t){return new F(e.span,e.target.visit(this),this.visitAll(e.args))}},{key:"visitLiteralArray",value:function(e,t){return new _(e.span,this.visitAll(e.expressions))}},{key:"visitLiteralMap",value:function(e,t){return new L(e.span,e.keys,this.visitAll(e.values))}},{key:"visitBinary",value:function(e,t){return new K(e.span,e.operation,e.left.visit(this),e.right.visit(this))}},{key:"visitPrefixNot",value:function(e,t){return new B(e.span,e.expression.visit(this))}},{key:"visitNonNullAssert",value:function(e,t){return new T(e.span,e.expression.visit(this))}},{key:"visitConditional",value:function(e,t){return new P(e.span,e.condition.visit(this),e.trueExp.visit(this),e.falseExp.visit(this))}},{key:"visitPipe",value:function(e,t){return new O(e.span,e.exp.visit(this),e.name,this.visitAll(e.args))}},{key:"visitKeyedRead",value:function(e,t){return new S(e.span,e.obj.visit(this),e.key.visit(this))}},{key:"visitKeyedWrite",value:function(e,t){return new N(e.span,e.obj.visit(this),e.key.visit(this),e.value.visit(this))}},{key:"visitAll",value:function(e){for(var t=new Array(e.length),n=0;n<e.length;++n)t[n]=e[n].visit(this);return t}},{key:"visitChain",value:function(e,t){return new C(e.span,this.visitAll(e.expressions))}},{key:"visitQuote",value:function(e,t){return new k(e.span,e.prefix,e.uninterpretedExpression,e.location)}}]),e}(),$=function(){function e(){a(this,e)}return u(e,[{key:"visitImplicitReceiver",value:function(e,t){return e}},{key:"visitInterpolation",value:function(e,t){var n=this.visitAll(e.expressions);return n!==e.expressions?new M(e.span,e.strings,n):e}},{key:"visitLiteralPrimitive",value:function(e,t){return e}},{key:"visitPropertyRead",value:function(e,t){var n=e.receiver.visit(this);return n!==e.receiver?new b(e.span,n,e.name):e}},{key:"visitPropertyWrite",value:function(e,t){var n=e.receiver.visit(this),i=e.value.visit(this);return n!==e.receiver||i!==e.value?new A(e.span,n,e.name,i):e}},{key:"visitSafePropertyRead",value:function(e,t){var n=e.receiver.visit(this);return n!==e.receiver?new E(e.span,n,e.name):e}},{key:"visitMethodCall",value:function(e,t){var n=e.receiver.visit(this),i=this.visitAll(e.args);return n!==e.receiver||i!==e.args?new R(e.span,n,e.name,i):e}},{key:"visitSafeMethodCall",value:function(e,t){var n=e.receiver.visit(this),i=this.visitAll(e.args);return n!==e.receiver||i!==e.args?new j(e.span,n,e.name,i):e}},{key:"visitFunctionCall",value:function(e,t){var n=e.target&&e.target.visit(this),i=this.visitAll(e.args);return n!==e.target||i!==e.args?new F(e.span,n,i):e}},{key:"visitLiteralArray",value:function(e,t){var n=this.visitAll(e.expressions);return n!==e.expressions?new _(e.span,n):e}},{key:"visitLiteralMap",value:function(e,t){var n=this.visitAll(e.values);return n!==e.values?new L(e.span,e.keys,n):e}},{key:"visitBinary",value:function(e,t){var n=e.left.visit(this),i=e.right.visit(this);return n!==e.left||i!==e.right?new K(e.span,e.operation,n,i):e}},{key:"visitPrefixNot",value:function(e,t){var n=e.expression.visit(this);return n!==e.expression?new B(e.span,n):e}},{key:"visitNonNullAssert",value:function(e,t){var n=e.expression.visit(this);return n!==e.expression?new T(e.span,n):e}},{key:"visitConditional",value:function(e,t){var n=e.condition.visit(this),i=e.trueExp.visit(this),r=e.falseExp.visit(this);return n!==e.condition||i!==e.trueExp||r!==e.falseExp?new P(e.span,n,i,r):e}},{key:"visitPipe",value:function(e,t){var n=e.exp.visit(this),i=this.visitAll(e.args);return n!==e.exp||i!==e.args?new O(e.span,n,e.name,i):e}},{key:"visitKeyedRead",value:function(e,t){var n=e.obj.visit(this),i=e.key.visit(this);return n!==e.obj||i!==e.key?new S(e.span,n,i):e}},{key:"visitKeyedWrite",value:function(e,t){var n=e.obj.visit(this),i=e.key.visit(this),r=e.value.visit(this);return n!==e.obj||i!==e.key||r!==e.value?new N(e.span,n,i,r):e}},{key:"visitAll",value:function(e){for(var t=new Array(e.length),n=!1,i=0;i<e.length;++i){var r=e[i],s=r.visit(this);t[i]=s,n=n||s!==r}return n?t:e}},{key:"visitChain",value:function(e,t){var n=this.visitAll(e.expressions);return n!==e.expressions?new C(e.span,n):e}},{key:"visitQuote",value:function(e,t){return e}}]),e}();var q;!function(e){e[e.DEFAULT=0]="DEFAULT",e[e.LITERAL_ATTR=1]="LITERAL_ATTR",e[e.ANIMATION=2]="ANIMATION"}(q||(q={}));var D,H=Object.freeze({__proto__:null,ParserError:y,ParseSpan:x,AST:g,Quote:k,EmptyExpr:m,ImplicitReceiver:w,Chain:C,Conditional:P,PropertyRead:b,PropertyWrite:A,SafePropertyRead:E,KeyedRead:S,KeyedWrite:N,BindingPipe:O,LiteralPrimitive:I,LiteralArray:_,LiteralMap:L,Interpolation:M,Binary:K,PrefixNot:B,NonNullAssert:T,MethodCall:R,SafeMethodCall:j,FunctionCall:F,AbsoluteSourceSpan:V,ASTWithSource:W,TemplateBinding:G,NullAstVisitor:U,RecursiveAstVisitor:Q,AstTransformer:z,AstMemoryEfficientTransformer:$,visitAstChildren:function(e,t,n){function i(e){t.visit&&t.visit(e,n)||e.visit(t,n)}function r(e){e.forEach(i)}e.visit({visitBinary:function(e){i(e.left),i(e.right)},visitChain:function(e){r(e.expressions)},visitConditional:function(e){i(e.condition),i(e.trueExp),i(e.falseExp)},visitFunctionCall:function(e){e.target&&i(e.target),r(e.args)},visitImplicitReceiver:function(e){},visitInterpolation:function(e){r(e.expressions)},visitKeyedRead:function(e){i(e.obj),i(e.key)},visitKeyedWrite:function(e){i(e.obj),i(e.key),i(e.obj)},visitLiteralArray:function(e){r(e.expressions)},visitLiteralMap:function(e){},visitLiteralPrimitive:function(e){},visitMethodCall:function(e){i(e.receiver),r(e.args)},visitPipe:function(e){i(e.exp),r(e.args)},visitPrefixNot:function(e){i(e.expression)},visitNonNullAssert:function(e){i(e.expression)},visitPropertyRead:function(e){i(e.receiver)},visitPropertyWrite:function(e){i(e.receiver),i(e.value)},visitQuote:function(e){},visitSafeMethodCall:function(e){i(e.receiver),r(e.args)},visitSafePropertyRead:function(e){i(e.receiver)}})},ParsedProperty:function e(t,n,i,r,s){a(this,e),this.name=t,this.expression=n,this.type=i,this.sourceSpan=r,this.valueSpan=s,this.isLiteral=this.type===q.LITERAL_ATTR,this.isAnimation=this.type===q.ANIMATION},get ParsedPropertyType(){return q},ParsedEvent:function e(t,n,i,r,s,o){a(this,e),this.name=t,this.targetOrPhase=n,this.type=i,this.handler=r,this.sourceSpan=s,this.handlerSpan=o},ParsedVariable:function e(t,n,i){a(this,e),this.name=t,this.value=n,this.sourceSpan=i},BoundElementProperty:function e(t,n,i,r,s,o,u){a(this,e),this.name=t,this.type=n,this.securityContext=i,this.value=r,this.unit=s,this.sourceSpan=o,this.valueSpan=u}}),J=0,X=9,Y=10,Z=11,ee=12,te=13,ne=32,ie=34,re=36,se=39,ae=43,oe=45,ue=48,le=57,ce=65,he=69,pe=90,ve=95,fe=97,de=101,ye=102,xe=110,ge=114,ke=116,me=118,we=122,Ce=160,Pe=96;function be(e){return ue<=e&&e<=le}!function(e){e[e.Character=0]="Character",e[e.Identifier=1]="Identifier",e[e.Keyword=2]="Keyword",e[e.String=3]="String",e[e.Operator=4]="Operator",e[e.Number=5]="Number",e[e.Error=6]="Error"}(D||(D={}));var Ae=["var","let","as","null","undefined","true","false","if","else","this"],Ee=function(){function e(){a(this,e)}return u(e,[{key:"tokenize",value:function(e){for(var t=new _e(e),n=[],i=t.scanToken();null!=i;)n.push(i),i=t.scanToken();return n}}]),e}(),Se=function(){function e(t,n,i,r){a(this,e),this.index=t,this.type=n,this.numValue=i,this.strValue=r}return u(e,[{key:"isCharacter",value:function(e){return this.type==D.Character&&this.numValue==e}},{key:"isNumber",value:function(){return this.type==D.Number}},{key:"isString",value:function(){return this.type==D.String}},{key:"isOperator",value:function(e){return this.type==D.Operator&&this.strValue==e}},{key:"isIdentifier",value:function(){return this.type==D.Identifier}},{key:"isKeyword",value:function(){return this.type==D.Keyword}},{key:"isKeywordLet",value:function(){return this.type==D.Keyword&&"let"==this.strValue}},{key:"isKeywordAs",value:function(){return this.type==D.Keyword&&"as"==this.strValue}},{key:"isKeywordNull",value:function(){return this.type==D.Keyword&&"null"==this.strValue}},{key:"isKeywordUndefined",value:function(){return this.type==D.Keyword&&"undefined"==this.strValue}},{key:"isKeywordTrue",value:function(){return this.type==D.Keyword&&"true"==this.strValue}},{key:"isKeywordFalse",value:function(){return this.type==D.Keyword&&"false"==this.strValue}},{key:"isKeywordThis",value:function(){return this.type==D.Keyword&&"this"==this.strValue}},{key:"isError",value:function(){return this.type==D.Error}},{key:"toNumber",value:function(){return this.type==D.Number?this.numValue:-1}},{key:"toString",value:function(){switch(this.type){case D.Character:case D.Identifier:case D.Keyword:case D.Operator:case D.String:case D.Error:return this.strValue;case D.Number:return this.numValue.toString();default:return null}}}]),e}();function Ne(e,t){return new Se(e,D.Character,t,String.fromCharCode(t))}function Oe(e,t){return new Se(e,D.Operator,0,t)}var Ie=new Se(-1,D.Character,0,""),_e=function(){function e(t){a(this,e),this.input=t,this.peek=0,this.index=-1,this.length=t.length,this.advance()}return u(e,[{key:"advance",value:function(){this.peek=++this.index>=this.length?J:this.input.charCodeAt(this.index)}},{key:"scanToken",value:function(){for(var e=this.input,t=this.length,n=this.peek,i=this.index;n<=ne;){if(++i>=t){n=J;break}n=e.charCodeAt(i)}if(this.peek=n,this.index=i,i>=t)return null;if(Le(n))return this.scanIdentifier();if(be(n))return this.scanNumber(i);var r,s=i;switch(n){case 46:return this.advance(),be(this.peek)?this.scanNumber(s):Ne(s,46);case 40:case 41:case 123:case 125:case 91:case 93:case 44:case 58:case 59:return this.scanCharacter(s,n);case se:case ie:return this.scanString();case 35:case ae:case oe:case 42:case 47:case 37:case 94:return this.scanOperator(s,String.fromCharCode(n));case 63:return this.scanComplexOperator(s,"?",46,".");case 60:case 62:return this.scanComplexOperator(s,String.fromCharCode(n),61,"=");case 33:case 61:return this.scanComplexOperator(s,String.fromCharCode(n),61,"=",61,"=");case 38:return this.scanComplexOperator(s,"&",38,"&");case 124:return this.scanComplexOperator(s,"|",124,"|");case Ce:for(;(r=this.peek)>=X&&r<=ne||r==Ce;)this.advance();return this.scanToken()}return this.advance(),this.error("Unexpected character [".concat(String.fromCharCode(n),"]"),0)}},{key:"scanCharacter",value:function(e,t){return this.advance(),Ne(e,t)}},{key:"scanOperator",value:function(e,t){return this.advance(),Oe(e,t)}},{key:"scanComplexOperator",value:function(e,t,n,i,r,s){this.advance();var a=t;return this.peek==n&&(this.advance(),a+=i),null!=r&&this.peek==r&&(this.advance(),a+=s),Oe(e,a)}},{key:"scanIdentifier",value:function(){var e=this.index;for(this.advance();Ke(this.peek);)this.advance();var t,n=this.input.substring(e,this.index);return Ae.indexOf(n)>-1?(t=n,new Se(e,D.Keyword,0,t)):function(e,t){return new Se(e,D.Identifier,0,t)}(e,n)}},{key:"scanNumber",value:function(e){var t,n=this.index===e;for(this.advance();;){if(be(this.peek));else if(46==this.peek)n=!1;else{if((t=this.peek)!=de&&t!=he)break;if(this.advance(),Be(this.peek)&&this.advance(),!be(this.peek))return this.error("Invalid exponent",-1);n=!1}this.advance()}var i,r=this.input.substring(e,this.index),s=n?function(e){var t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}(r):parseFloat(r);return i=s,new Se(e,D.Number,i,"")}},{key:"scanString",value:function(){var e=this.index,t=this.peek;this.advance();for(var n="",i=this.index,r=this.input;this.peek!=t;)if(92==this.peek){n+=r.substring(i,this.index),this.advance();var s=void 0;if(this.peek=this.peek,117==this.peek){var a=r.substring(this.index+1,this.index+5);if(!/^[0-9a-f]+$/i.test(a))return this.error("Invalid unicode escape [\\u".concat(a,"]"),0);s=parseInt(a,16);for(var o=0;o<5;o++)this.advance()}else s=Re(this.peek),this.advance();n+=String.fromCharCode(s),i=this.index}else{if(this.peek==J)return this.error("Unterminated quote",0);this.advance()}var u,l=r.substring(i,this.index);return this.advance(),u=n+l,new Se(e,D.String,0,u)}},{key:"error",value:function(e,t){var n=this.index+t;return function(e,t){return new Se(e,D.Error,0,t)}(n,"Lexer Error: ".concat(e," at column ").concat(n," in expression [").concat(this.input,"]"))}}]),e}();function Le(e){return fe<=e&&e<=we||ce<=e&&e<=pe||e==ve||e==re}function Me(e){if(0==e.length)return!1;var t=new _e(e);if(!Le(t.peek))return!1;for(t.advance();t.peek!==J;){if(!Ke(t.peek))return!1;t.advance()}return!0}function Ke(e){return function(e){return e>=fe&&e<=we||e>=ce&&e<=pe}(e)||be(e)||e==ve||e==re}function Be(e){return e==oe||e==ae}function Te(e){return e===se||e===ie||e===Pe}function Re(e){switch(e){case xe:return Y;case ye:return ee;case ge:return te;case ke:return X;case me:return Z;default:return e}}var je=Object.freeze({__proto__:null,get TokenType(){return D},Lexer:Ee,Token:Se,EOF:Ie,isIdentifier:Me,isQuote:Te}),Fe=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */var Ve=new(function(){function e(t,n){a(this,e),this.start=t,this.end=n}return u(e,null,[{key:"fromArray",value:function(t){return t?(function(e,t){if(!(null==t||Array.isArray(t)&&2==t.length))throw new Error("Expected '".concat(e,"' to be an array, [start, end]."));if(null!=t){var n=t[0],i=t[1];Fe.forEach((function(e){if(e.test(n)||e.test(i))throw new Error("['".concat(n,"', '").concat(i,"'] contains unusable interpolation symbol."))}))}}("interpolation",t),new e(t[0],t[1])):Ve}}]),e}())("{{","}}");function We(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}"undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self;var Ge=function e(t,n,i){a(this,e),this.strings=t,this.expressions=n,this.offsets=i},Ue=function e(t,n,i){a(this,e),this.templateBindings=t,this.warnings=n,this.errors=i};function Qe(e){var t=We(e.start)+"([\\s\\S]*?)"+We(e.end);return new RegExp(t,"g")}var ze=function(){function e(t){a(this,e),this._lexer=t,this.errors=[]}return u(e,[{key:"parseAction",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Ve;this._checkNoInterpolation(e,t,i);var r=this._stripComments(e),s=this._lexer.tokenize(this._stripComments(e)),a=new $e(e,t,n,s,r.length,!0,this.errors,e.length-r.length).parseChain();return new W(a,e,t,n,this.errors)}},{key:"parseBinding",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Ve,r=this._parseBindingAst(e,t,n,i);return new W(r,e,t,n,this.errors)}},{key:"parseSimpleBinding",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Ve,r=this._parseBindingAst(e,t,n,i),s=qe.check(r);return s.length>0&&this._reportError("Host binding expression cannot contain ".concat(s.join(" ")),e,t),new W(r,e,t,n,this.errors)}},{key:"_reportError",value:function(e,t,n,i){this.errors.push(new y(e,t,n,i))}},{key:"_parseBindingAst",value:function(e,t,n,i){var r=this._parseQuote(e,t);if(null!=r)return r;this._checkNoInterpolation(e,t,i);var s=this._stripComments(e),a=this._lexer.tokenize(s);return new $e(e,t,n,a,s.length,!1,this.errors,e.length-s.length).parseChain()}},{key:"_parseQuote",value:function(e,t){if(null==e)return null;var n=e.indexOf(":");if(-1==n)return null;var i=e.substring(0,n).trim();if(!Me(i))return null;var r=e.substring(n+1);return new k(new x(0,e.length),i,r,t)}},{key:"parseTemplateBindings",value:function(e,t,n,i){var r=this._lexer.tokenize(t);return new $e(t,n,i,r,t.length,!1,this.errors,0).parseTemplateBindings(e)}},{key:"parseInterpolation",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Ve,r=this.splitInterpolation(e,t,i);if(null==r)return null;for(var s=[],a=0;a<r.expressions.length;++a){var o=r.expressions[a],u=this._stripComments(o),l=this._lexer.tokenize(u),c=new $e(e,t,n,l,u.length,!1,this.errors,r.offsets[a]+(o.length-u.length)).parseChain();s.push(c)}return new W(new M(new x(0,null==e?0:e.length),r.strings,s),e,t,n,this.errors)}},{key:"splitInterpolation",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Ve,i=Qe(n),r=e.split(i);if(r.length<=1)return null;for(var s=[],a=[],o=[],u=0,l=0;l<r.length;l++){var c=r[l];l%2==0?(s.push(c),u+=c.length):c.trim().length>0?(u+=n.start.length,a.push(c),o.push(u),u+=c.length+n.end.length):(this._reportError("Blank expressions are not allowed in interpolated strings",e,"at column ".concat(this._findInterpolationErrorColumn(r,l,n)," in"),t),a.push("$implict"),o.push(u))}return new Ge(s,a,o)}},{key:"wrapLiteralPrimitive",value:function(e,t,n){return new W(new I(new x(0,null==e?0:e.length),e),e,t,n,this.errors)}},{key:"_stripComments",value:function(e){var t=this._commentStart(e);return null!=t?e.substring(0,t).trim():e}},{key:"_commentStart",value:function(e){for(var t=null,n=0;n<e.length-1;n++){var i=e.charCodeAt(n),r=e.charCodeAt(n+1);if(47===i&&47==r&&null==t)return n;t===i?t=null:null==t&&Te(i)&&(t=i)}return null}},{key:"_checkNoInterpolation",value:function(e,t,n){var i=Qe(n),r=e.split(i);r.length>1&&this._reportError("Got interpolation (".concat(n.start).concat(n.end,") where expression was expected"),e,"at column ".concat(this._findInterpolationErrorColumn(r,1,n)," in"),t)}},{key:"_findInterpolationErrorColumn",value:function(e,t,n){for(var i="",r=0;r<t;r++)i+=r%2==0?e[r]:"".concat(n.start).concat(e[r]).concat(n.end);return i.length}}]),e}(),$e=function(){function e(t,n,i,r,s,o,u,l){a(this,e),this.input=t,this.location=n,this.absoluteOffset=i,this.tokens=r,this.inputLength=s,this.parseAction=o,this.errors=u,this.offset=l,this.rparensExpected=0,this.rbracketsExpected=0,this.rbracesExpected=0,this.index=0}return u(e,[{key:"peek",value:function(e){var t=this.index+e;return t<this.tokens.length?this.tokens[t]:Ie}},{key:"span",value:function(e){return new x(e,this.inputIndex)}},{key:"advance",value:function(){this.index++}},{key:"optionalCharacter",value:function(e){return!!this.next.isCharacter(e)&&(this.advance(),!0)}},{key:"peekKeywordLet",value:function(){return this.next.isKeywordLet()}},{key:"peekKeywordAs",value:function(){return this.next.isKeywordAs()}},{key:"expectCharacter",value:function(e){this.optionalCharacter(e)||this.error("Missing expected ".concat(String.fromCharCode(e)))}},{key:"optionalOperator",value:function(e){return!!this.next.isOperator(e)&&(this.advance(),!0)}},{key:"expectOperator",value:function(e){this.optionalOperator(e)||this.error("Missing expected operator ".concat(e))}},{key:"expectIdentifierOrKeyword",value:function(){var e=this.next;return e.isIdentifier()||e.isKeyword()?(this.advance(),e.toString()):(this.error("Unexpected token ".concat(e,", expected identifier or keyword")),"")}},{key:"expectIdentifierOrKeywordOrString",value:function(){var e=this.next;return e.isIdentifier()||e.isKeyword()||e.isString()?(this.advance(),e.toString()):(this.error("Unexpected token ".concat(e,", expected identifier, keyword, or string")),"")}},{key:"parseChain",value:function(){for(var e=[],t=this.inputIndex;this.index<this.tokens.length;){var n=this.parsePipe();if(e.push(n),this.optionalCharacter(59))for(this.parseAction||this.error("Binding expression cannot contain chained expression");this.optionalCharacter(59););else this.index<this.tokens.length&&this.error("Unexpected token '".concat(this.next,"'"))}return 0==e.length?new m(this.span(t)):1==e.length?e[0]:new C(this.span(t),e)}},{key:"parsePipe",value:function(){var e=this.parseExpression();if(this.optionalOperator("|")){this.parseAction&&this.error("Cannot have a pipe in an action expression");do{for(var t=this.expectIdentifierOrKeyword(),n=[];this.optionalCharacter(58);)n.push(this.parseExpression());e=new O(this.span(e.span.start),e,t,n)}while(this.optionalOperator("|"))}return e}},{key:"parseExpression",value:function(){return this.parseConditional()}},{key:"parseConditional",value:function(){var e=this.inputIndex,t=this.parseLogicalOr();if(this.optionalOperator("?")){var n,i=this.parsePipe();if(this.optionalCharacter(58))n=this.parsePipe();else{var r=this.inputIndex,s=this.input.substring(e,r);this.error("Conditional expression ".concat(s," requires all 3 expressions")),n=new m(this.span(e))}return new P(this.span(e),t,i,n)}return t}},{key:"parseLogicalOr",value:function(){for(var e=this.parseLogicalAnd();this.optionalOperator("||");){var t=this.parseLogicalAnd();e=new K(this.span(e.span.start),"||",e,t)}return e}},{key:"parseLogicalAnd",value:function(){for(var e=this.parseEquality();this.optionalOperator("&&");){var t=this.parseEquality();e=new K(this.span(e.span.start),"&&",e,t)}return e}},{key:"parseEquality",value:function(){for(var e=this.parseRelational();this.next.type==D.Operator;){var t=this.next.strValue;switch(t){case"==":case"===":case"!=":case"!==":this.advance();var n=this.parseRelational();e=new K(this.span(e.span.start),t,e,n);continue}break}return e}},{key:"parseRelational",value:function(){for(var e=this.parseAdditive();this.next.type==D.Operator;){var t=this.next.strValue;switch(t){case"<":case">":case"<=":case">=":this.advance();var n=this.parseAdditive();e=new K(this.span(e.span.start),t,e,n);continue}break}return e}},{key:"parseAdditive",value:function(){for(var e=this.parseMultiplicative();this.next.type==D.Operator;){var t=this.next.strValue;switch(t){case"+":case"-":this.advance();var n=this.parseMultiplicative();e=new K(this.span(e.span.start),t,e,n);continue}break}return e}},{key:"parseMultiplicative",value:function(){for(var e=this.parsePrefix();this.next.type==D.Operator;){var t=this.next.strValue;switch(t){case"*":case"%":case"/":this.advance();var n=this.parsePrefix();e=new K(this.span(e.span.start),t,e,n);continue}break}return e}},{key:"parsePrefix",value:function(){if(this.next.type==D.Operator){var e,t=this.inputIndex,n=this.next.strValue;switch(n){case"+":return this.advance(),e=this.parsePrefix(),new K(this.span(t),"-",e,new I(new x(t,t),0));case"-":return this.advance(),e=this.parsePrefix(),new K(this.span(t),n,new I(new x(t,t),0),e);case"!":return this.advance(),e=this.parsePrefix(),new B(this.span(t),e)}}return this.parseCallChain()}},{key:"parseCallChain",value:function(){for(var e=this.parsePrimary();;)if(this.optionalCharacter(46))e=this.parseAccessMemberOrMethodCall(e,!1);else if(this.optionalOperator("?."))e=this.parseAccessMemberOrMethodCall(e,!0);else if(this.optionalCharacter(91)){this.rbracketsExpected++;var t=this.parsePipe();if(this.rbracketsExpected--,this.expectCharacter(93),this.optionalOperator("=")){var n=this.parseConditional();e=new N(this.span(e.span.start),e,t,n)}else e=new S(this.span(e.span.start),e,t)}else if(this.optionalCharacter(40)){this.rparensExpected++;var i=this.parseCallArguments();this.rparensExpected--,this.expectCharacter(41),e=new F(this.span(e.span.start),e,i)}else{if(!this.optionalOperator("!"))return e;e=new T(this.span(e.span.start),e)}}},{key:"parsePrimary",value:function(){var e=this.inputIndex;if(this.optionalCharacter(40)){this.rparensExpected++;var t=this.parsePipe();return this.rparensExpected--,this.expectCharacter(41),t}if(this.next.isKeywordNull())return this.advance(),new I(this.span(e),null);if(this.next.isKeywordUndefined())return this.advance(),new I(this.span(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new I(this.span(e),!0);if(this.next.isKeywordFalse())return this.advance(),new I(this.span(e),!1);if(this.next.isKeywordThis())return this.advance(),new w(this.span(e));if(this.optionalCharacter(91)){this.rbracketsExpected++;var n=this.parseExpressionList(93);return this.rbracketsExpected--,this.expectCharacter(93),new _(this.span(e),n)}if(this.next.isCharacter(123))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMemberOrMethodCall(new w(this.span(e)),!1);if(this.next.isNumber()){var i=this.next.toNumber();return this.advance(),new I(this.span(e),i)}if(this.next.isString()){var r=this.next.toString();return this.advance(),new I(this.span(e),r)}return this.index>=this.tokens.length?(this.error("Unexpected end of expression: ".concat(this.input)),new m(this.span(e))):(this.error("Unexpected token ".concat(this.next)),new m(this.span(e)))}},{key:"parseExpressionList",value:function(e){var t=[];if(!this.next.isCharacter(e))do{t.push(this.parsePipe())}while(this.optionalCharacter(44));return t}},{key:"parseLiteralMap",value:function(){var e=[],t=[],n=this.inputIndex;if(this.expectCharacter(123),!this.optionalCharacter(125)){this.rbracesExpected++;do{var i=this.next.isString(),r=this.expectIdentifierOrKeywordOrString();e.push({key:r,quoted:i}),this.expectCharacter(58),t.push(this.parsePipe())}while(this.optionalCharacter(44));this.rbracesExpected--,this.expectCharacter(125)}return new L(this.span(n),e,t)}},{key:"parseAccessMemberOrMethodCall",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.span.start,i=this.expectIdentifierOrKeyword();if(this.optionalCharacter(40)){this.rparensExpected++;var r=this.parseCallArguments();this.expectCharacter(41),this.rparensExpected--;var s=this.span(n);return t?new j(s,e,i,r):new R(s,e,i,r)}if(t)return this.optionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),new m(this.span(n))):new E(this.span(n),e,i);if(this.optionalOperator("=")){if(!this.parseAction)return this.error("Bindings cannot contain assignments"),new m(this.span(n));var a=this.parseConditional();return new A(this.span(n),e,i,a)}return new b(this.span(n),e,i)}},{key:"parseCallArguments",value:function(){if(this.next.isCharacter(41))return[];var e=[];do{e.push(this.parsePipe())}while(this.optionalCharacter(44));return e}},{key:"expectTemplateBindingKey",value:function(){var e="",t=!1;do{e+=this.expectIdentifierOrKeywordOrString(),(t=this.optionalOperator("-"))&&(e+="-")}while(t);return e.toString()}},{key:"parseTemplateBindings",value:function(e){var t=!0,n=[];do{var i=this.inputIndex,r=void 0,s=void 0,a=!1;t?(r=s=e,t=!1):((a=this.peekKeywordLet())&&this.advance(),r=this.expectTemplateBindingKey(),s=a?r:e+r[0].toUpperCase()+r.substring(1),this.optionalCharacter(58));var o=null,u=null;if(a)o=this.optionalOperator("=")?this.expectTemplateBindingKey():"$implicit";else if(this.peekKeywordAs())this.advance(),o=r,s=this.expectTemplateBindingKey(),a=!0;else if(this.next!==Ie&&!this.peekKeywordLet()){var l=this.inputIndex,c=this.parsePipe(),h=this.input.substring(l-this.offset,this.inputIndex-this.offset);u=new W(c,h,this.location,this.absoluteOffset,this.errors)}if(n.push(new G(this.span(i),s,a,o,u)),this.peekKeywordAs()&&!a){var p=this.inputIndex;this.advance();var v=this.expectTemplateBindingKey();n.push(new G(this.span(p),v,!0,s,null))}this.optionalCharacter(59)||this.optionalCharacter(44)}while(this.index<this.tokens.length);return new Ue(n,[],this.errors)}},{key:"error",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.errors.push(new y(e,this.input,this.locationText(t),this.location)),this.skip()}},{key:"locationText",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null==e&&(e=this.index),e<this.tokens.length?"at column ".concat(this.tokens[e].index+1," in"):"at the end of the expression"}},{key:"skip",value:function(){for(var e=this.next;this.index<this.tokens.length&&!e.isCharacter(59)&&(this.rparensExpected<=0||!e.isCharacter(41))&&(this.rbracesExpected<=0||!e.isCharacter(125))&&(this.rbracketsExpected<=0||!e.isCharacter(93));)this.next.isError()&&this.errors.push(new y(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}},{key:"next",get:function(){return this.peek(0)}},{key:"inputIndex",get:function(){return this.index<this.tokens.length?this.next.index+this.offset:this.inputLength+this.offset}}]),e}(),qe=function(){function e(){a(this,e),this.errors=[]}return u(e,[{key:"visitImplicitReceiver",value:function(e,t){}},{key:"visitInterpolation",value:function(e,t){}},{key:"visitLiteralPrimitive",value:function(e,t){}},{key:"visitPropertyRead",value:function(e,t){}},{key:"visitPropertyWrite",value:function(e,t){}},{key:"visitSafePropertyRead",value:function(e,t){}},{key:"visitMethodCall",value:function(e,t){}},{key:"visitSafeMethodCall",value:function(e,t){}},{key:"visitFunctionCall",value:function(e,t){}},{key:"visitLiteralArray",value:function(e,t){this.visitAll(e.expressions)}},{key:"visitLiteralMap",value:function(e,t){this.visitAll(e.values)}},{key:"visitBinary",value:function(e,t){}},{key:"visitPrefixNot",value:function(e,t){}},{key:"visitNonNullAssert",value:function(e,t){}},{key:"visitConditional",value:function(e,t){}},{key:"visitPipe",value:function(e,t){this.errors.push("pipes")}},{key:"visitKeyedRead",value:function(e,t){}},{key:"visitKeyedWrite",value:function(e,t){}},{key:"visitAll",value:function(e){var t=this;return e.map((function(e){return e.visit(t)}))}},{key:"visitChain",value:function(e,t){}},{key:"visitQuote",value:function(e,t){}}],[{key:"check",value:function(t){var n=new e;return t.visit(n),n.errors}}]),e}(),De=Object.freeze({__proto__:null,SplitInterpolation:Ge,TemplateBindingParseResult:Ue,Parser:ze,_ParseAST:$e}),He=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="angular-estree-parser",i="NgEstreeParser";function r(){return new De.Parser(new je.Lexer)}function a(e,t){var n=r(),i=u(e,n),s=i.astInput,a=i.comments,l=t(s,n),c=l.ast;return o(l.errors),{ast:c,comments:a}}function o(e){if(0!==e.length){var t=v(e,1)[0].message;throw new SyntaxError(t.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}}function u(e,t){var n=t._commentStart(e);return null===n?{astInput:e,comments:[]}:{astInput:e.slice(0,n),comments:[{type:"Comment",value:e.slice(n+"//".length),span:{start:n,end:e.length}}]}}function l(e,t){for(var n=e.start,i=e.end;i!==n&&/\s/.test(t[i-1]);)i--;for(;n!==i&&/\s/.test(t[n]);)n++;return{start:n,end:i}}function c(e,t){for(var n=e.start,i=e.end;i!==t.length&&/\s/.test(t[i]);)i++;for(;0!==n&&/\s/.test(t[n-1]);)n--;return{start:n,end:i}}function h(e,t){return"("===t[e.start-1]&&")"===t[e.end]?{start:e.start-1,end:e.end+1}:e}t.parseNgBinding=function(e){return a(e,(function(e,t){return t.parseBinding(e,n)}))},t.parseNgSimpleBinding=function(e){return a(e,(function(e,t){return t.parseSimpleBinding(e,n)}))},t.parseNgAction=function(e){return a(e,(function(e,t){return t.parseAction(e,n)}))},t.parseNgTemplateBindings=function(e){var t=r().parseTemplateBindings(i,e,n),s=t.templateBindings;return o(t.errors),s},t.parseNgInterpolation=function(e){var t=r(),i=u(e,t),a=i.astInput,l=i.comments,c=t.parseInterpolation("{{"+a+"}}",n),h=c.ast;o(c.errors);var p=h.expressions[0];return function e(t,n){if(!t||"object"!==s(t))return;if(Array.isArray(t))return t.forEach((function(t){return e(t,n)}));for(var i=0,r=Object.keys(t);i<r.length;i++){var a=r[i],o=t[a];"span"===a?n(o):e(o,n)}}(p,(function(e){e.start-="{{".length,e.end-="{{".length})),{ast:p,comments:l}},t.getNgType=function(e){return e instanceof H.Binary?"Binary":e instanceof H.BindingPipe?"BindingPipe":e instanceof H.Chain?"Chain":e instanceof H.Conditional?"Conditional":e instanceof H.EmptyExpr?"EmptyExpr":e instanceof H.FunctionCall?"FunctionCall":e instanceof H.ImplicitReceiver?"ImplicitReceiver":e instanceof H.KeyedRead?"KeyedRead":e instanceof H.KeyedWrite?"KeyedWrite":e instanceof H.LiteralArray?"LiteralArray":e instanceof H.LiteralMap?"LiteralMap":e instanceof H.LiteralPrimitive?"LiteralPrimitive":e instanceof H.MethodCall?"MethodCall":e instanceof H.NonNullAssert?"NonNullAssert":e instanceof H.PrefixNot?"PrefixNot":e instanceof H.PropertyRead?"PropertyRead":e instanceof H.PropertyWrite?"PropertyWrite":e instanceof H.Quote?"Quote":e instanceof H.SafeMethodCall?"SafeMethodCall":e instanceof H.SafePropertyRead?"SafePropertyRead":e.type},t.fitSpans=function(e,t,n){for(var i=0,r={start:e.start,end:e.end};;){var s=c(r,t),a=h(s,t);if(s.start===a.start&&s.end===a.end)break;r.start=a.start,r.end=a.end,i++}return{hasParens:0!==(n?i-1:i),outerSpan:l(n?{start:r.start+1,end:r.end-1}:r,t),innerSpan:l(e,t)}},t.findFrontChar=function(e,t,n){for(var i=t;!e.test(n[i]);)i--;return i},t.findBackChar=function(e,t,n){for(var i=t;!e.test(n[i]);)i++;return i},t.toLowerCamelCase=function(e){return e.slice(0,1).toLowerCase()+e.slice(1)},t.getLast=function(e){return 0===e.length?void 0:e[e.length-1]}}));i(He);He.parseNgBinding,He.parseNgSimpleBinding,He.parseNgAction,He.parseNgTemplateBindings,He.parseNgInterpolation,He.getNgType,He.fitSpans,He.findFrontChar,He.findBackChar,He.toLowerCamelCase,He.getLast;var Je=r((function(e,t){function n(e,t,n,i){if(!n){var r=e.start,s=e.end;return{start:r,end:s,loc:{start:t.locator.locationForIndex(r),end:t.locator.locationForIndex(s)}}}var a=He.fitSpans(e,t.text,i),o=a.outerSpan,u=a.innerSpan,l=a.hasParens;return Object.assign({start:u.start,end:u.end,loc:{start:t.locator.locationForIndex(u.start),end:t.locator.locationForIndex(u.end)}},l&&{extra:{parenthesized:!0,parenStart:o.start,parenEnd:o.end}})}Object.defineProperty(t,"__esModule",{value:!0}),t.transform=function(e,i){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=He.getNgType(e);switch(a){case"Binary":var o=e.left,u=e.operation,l=e.right,c=l.span.start===l.span.end,h=o.span.start===o.span.end;if(c||h){var p=o.span.start===o.span.end?Pe(l):Pe(o);return Ae("UnaryExpression",{prefix:!0,argument:p,operator:c?"+":"-"},{start:e.span.start,end:Le(p)},{hasParentParens:r})}var v=Pe(o),f=Pe(l);return Ae("&&"===u||"||"===u?"LogicalExpression":"BinaryExpression",{left:v,right:f,operator:u},{start:_e(v),end:Le(f)},{hasParentParens:r});case"BindingPipe":var d=e.exp,y=e.name,x=e.args,g=Pe(d),k=Ne(/\S/,Ne(/\|/,Le(g))+1),m=Ae("Identifier",{name:y},{start:k,end:k+y.length}),w=x.map(Pe);return Ae("NGPipeExpression",{left:g,right:m,arguments:w},{start:_e(g),end:Le(0===w.length?m:He.getLast(w))},{hasParentParens:r});case"Chain":var C=e.expressions;return Ae("NGChainedExpression",{expressions:C.map(Pe)},e.span,{hasParentParens:r});case"Comment":var P=e.value;return Ae("CommentLine",{value:P},e.span,{processSpan:!1});case"Conditional":var b=e.condition,A=e.trueExp,E=e.falseExp,S=Pe(b),N=Pe(A),O=Pe(E);return Ae("ConditionalExpression",{test:S,consequent:N,alternate:O},{start:_e(S),end:Le(O)},{hasParentParens:r});case"EmptyExpr":return Ae("NGEmptyExpression",{},e.span,{hasParentParens:r});case"FunctionCall":var I=e.target,_=e.args,L=1===_.length?[be(_[0])]:_.map(Pe),M=Pe(I);return Ae("CallExpression",{callee:M,arguments:L},{start:_e(M),end:e.span.end},{hasParentParens:r});case"KeyedRead":var K=e.obj,B=e.key,T=Pe(K),R=Pe(B);return Ae("MemberExpression",{computed:!0,object:T,property:R},{start:_e(T),end:e.span.end},{hasParentParens:r});case"LiteralArray":var j=e.expressions;return Ae("ArrayExpression",{elements:j.map(Pe)},e.span,{hasParentParens:r});case"LiteralMap":var F=e.keys,V=e.values,W=V.map((function(e){return Pe(e)})),G=F.map((function(t,n){var i=t.key,r=t.quoted,s=W[n],a={start:Ne(/\S/,0===n?e.span.start+1:Ne(/,/,Le(W[n-1]))+1),end:Se(/\S/,Se(/:/,_e(s)-1)-1)+1},o=r?Ae("StringLiteral",{value:i},a):Ae("Identifier",{name:i},a);return Ae("ObjectProperty",{key:o,value:s,method:!1,shorthand:!1,computed:!1},{start:_e(o),end:Le(s)})}));return Ae("ObjectExpression",{properties:G},e.span,{hasParentParens:r});case"LiteralPrimitive":var U=e.value;switch(s(U)){case"boolean":return Ae("BooleanLiteral",{value:U},e.span,{hasParentParens:r});case"number":return Ae("NumericLiteral",{value:U},e.span,{hasParentParens:r});case"object":return Ae("NullLiteral",{},e.span,{hasParentParens:r});case"string":return Ae("StringLiteral",{value:U},e.span,{hasParentParens:r});case"undefined":return Ae("Identifier",{name:"undefined"},e.span,{hasParentParens:r});default:throw new Error("Unexpected LiteralPrimitive value type ".concat(s(U)))}case"MethodCall":case"SafeMethodCall":var Q="SafeMethodCall"===a,z=e.receiver,$=e.name,q=e.args,D=1===q.length?[be(q[0])]:q.map(Pe),H=Se(/\S/,Se(/\(/,(0===D.length?Se(/\)/,e.span.end-1):_e(D[0]))-1)-1)+1,J=Ae("Identifier",{name:$},{start:H-$.length,end:H}),X=Ee(z,J,{computed:!1,optional:Q}),Y=Oe(X);return Ae(Q||Y?"OptionalCallExpression":"CallExpression",{callee:X,arguments:D},{start:_e(X),end:e.span.end},{hasParentParens:r});case"NonNullAssert":var Z=e.expression,ee=Pe(Z);return Ae("TSNonNullExpression",{expression:ee},{start:_e(ee),end:e.span.end},{hasParentParens:r});case"PrefixNot":var te=e.expression,ne=Pe(te);return Ae("UnaryExpression",{prefix:!0,operator:"!",argument:ne},{start:e.span.start,end:Le(ne)},{hasParentParens:r});case"PropertyRead":case"SafePropertyRead":var ie="SafePropertyRead"===a,re=e.receiver,se=e.name,ae=Se(/\S/,e.span.end-1)+1,oe=Ae("Identifier",{name:se},{start:ae-se.length,end:ae},re.span.start===re.span.end?{hasParentParens:r}:{});return Ee(re,oe,{computed:!1,optional:ie},{hasParentParens:r});case"KeyedWrite":var ue=e.obj,le=e.key,ce=e.value,he=Pe(le),pe=Pe(ce),ve=Ee(ue,he,{computed:!0,optional:!1},{end:Ne(/\]/,Le(he))+1});return Ae("AssignmentExpression",{left:ve,operator:"=",right:pe},{start:_e(ve),end:Le(pe)},{hasParentParens:r});case"PropertyWrite":var fe=e.receiver,de=e.name,ye=e.value,xe=Pe(ye),ge=Se(/\S/,Se(/=/,_e(xe)-1)-1)+1,ke=Ae("Identifier",{name:de},{start:ge-de.length,end:ge}),me=Ee(fe,ke,{computed:!1,optional:!1});return Ae("AssignmentExpression",{left:me,operator:"=",right:xe},{start:_e(me),end:Le(xe)},{hasParentParens:r});case"Quote":var we=e.prefix,Ce=e.uninterpretedExpression;return Ae("NGQuotedExpression",{prefix:we,value:Ce},e.span,{hasParentParens:r});default:throw new Error("Unexpected node ".concat(a))}function Pe(e){return t.transform(e,i)}function be(e){return t.transform(e,i,!0)}function Ae(e,t,r){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=s.processSpan,o=void 0===a||a,u=s.hasParentParens,l=void 0!==u&&u,c=Object.assign({type:e},n(r,i,o,l),t);switch(e){case"Identifier":var h=c;h.loc.identifierName=h.name;break;case"NumericLiteral":var p=c;p.extra=Object.assign({},p.extra,{raw:i.text.slice(p.start,p.end),rawValue:p.value});break;case"StringLiteral":var v=c;v.extra=Object.assign({},v.extra,{raw:i.text.slice(v.start,v.end),rawValue:v.value})}return c}function Ee(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=i.end,s=void 0===r?Le(t):r,a=i.hasParentParens,o=void 0!==a&&a;if(e.span.start===e.span.end)return t;var u="ImplicitReceiver"===He.getNgType(e)?Ae("ThisExpression",{},e.span):Pe(e),l=Oe(u);return Ae(n.optional||l?"OptionalMemberExpression":"MemberExpression",Object.assign({object:u,property:t,computed:n.computed},n.optional?{optional:!0}:l?{optional:!1}:null),{start:_e(u),end:s},{hasParentParens:o})}function Se(e,t){return He.findFrontChar(e,t,i.text)}function Ne(e,t){return He.findBackChar(e,t,i.text)}function Oe(e){return("OptionalCallExpression"===e.type||"OptionalMemberExpression"===e.type)&&!Ie(e)}function Ie(e){return e.extra&&e.extra.parenthesized}function _e(e){return Ie(e)?e.extra.parenStart:e.start}function Le(e){return Ie(e)?e.extra.parenEnd:e.end}},t.transformSpan=n}));i(Je);Je.transform,Je.transformSpan;var Xe=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.transformTemplateBindings=function(e,t){for(var n=v(e,1)[0],i=n.key,r=0===t.text.slice(n.span.start,n.span.end).trim().length?e.slice(1):e,s=[],a=null,o=0;o<r.length;o++){var u=r[o],l=u.key,c=u.keyIsVar,h=u.name,p=u.span;a&&a.key===h&&c&&/^as\s$/.test(t.text.slice(p.start,p.start+3))?function(){var e=x(He.findBackChar(/\S/,p.start+3,t.text),l),n=y("NGMicrosyntaxKey",{name:l},e),i=function(e,n){return Object.assign({},e,Je.transformSpan({start:e.start,end:n},t))},r=function(e){return Object.assign({},i(e,n.end),{alias:n})},a=s.pop();if("NGMicrosyntaxExpression"===a.type)s.push(r(a));else{if("NGMicrosyntaxKeyedExpression"!==a.type)throw new Error("Unexpected type ".concat(a.type));var o=r(a.expression);s.push(i(Object.assign({},a,{expression:o}),o.end))}}():s.push(f(u,o)),a=u}return y("NGMicrosyntax",{body:s},0===s.length?e[0].span:{start:s[0].start,end:s[s.length-1].end});function f(e,n){var i=e.key,r=e.keyIsVar,s=e.name,a=e.expression,o=e.span;if(r){if(/^let\s$/.test(t.text.slice(o.start,o.start+4))){var u=x(He.findBackChar(/\S/,o.start+4,t.text),i);return y("NGMicrosyntaxLet",{key:y("NGMicrosyntaxKey",{name:i},u),value:0===t.text.slice(u.end,o.end).trim().length?null:y("NGMicrosyntaxKey",{name:s},{start:He.findBackChar(/=/,u.end,t.text)+1,end:o.end})},o)}var l=x(o.start,s);return y("NGMicrosyntaxAs",{key:y("NGMicrosyntaxKey",{name:s},l),alias:y("NGMicrosyntaxKey",{name:i},{start:He.findBackChar(/\S/,l.end,t.text)+"as".length,end:o.end})},o)}if(a){if(0===n)return y("NGMicrosyntaxExpression",{expression:d(a.ast),alias:null},o);var c=d(a.ast),h=c.start,p=c.end,v=g(i);return y("NGMicrosyntaxKeyedExpression",{key:y("NGMicrosyntaxKey",{name:v},x(o.start,v)),expression:y("NGMicrosyntaxExpression",{expression:c,alias:null},{start:h,end:p})},o)}return y("NGMicrosyntaxKey",{name:g(i)},o)}function d(e){return Je.transform(e,t)}function y(e,n,i){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return Object.assign({type:e},Je.transformSpan(i,t,r,!1),n)}function x(e,n){if("'"!==t.text[e]&&'"'!==t.text[e])return{start:e,end:e+n.length};for(var i=t.text[e],r=0,s=e+1;;){var a=t.text[s];if(a===i&&r%2==0)return{start:e,end:s+1};"\\"===a?r++:r=0,s++}}function g(e){return He.toLowerCamelCase(e.slice(i.length))}}}));i(Xe);Xe.transformTemplateBindings;var Ye=r((function(e,t){function n(e,t){var n=t(e),i=n.ast,r=n.comments,s=new d.Context(e),a=function(e){return Je.transform(e,s)},o=a(i);return o.comments=r.map(a),o}Object.defineProperty(t,"__esModule",{value:!0}),t.parseBinding=function(e){return n(e,He.parseNgBinding)},t.parseSimpleBinding=function(e){return n(e,He.parseNgSimpleBinding)},t.parseInterpolation=function(e){return n(e,He.parseNgInterpolation)},t.parseAction=function(e){return n(e,He.parseNgAction)},t.parseTemplateBindings=function(e){return Xe.transformTemplateBindings(He.parseNgTemplateBindings(e),new d.Context(e))}}));i(Ye);Ye.parseBinding,Ye.parseSimpleBinding,Ye.parseInterpolation,Ye.parseAction,Ye.parseTemplateBindings;function Ze(e){return Object.assign({astFormat:"estree",parse:function(t,n,i){var r=e(t,Ye);return{type:"NGRoot",node:"__ng_action"===i.parser&&"NGChainedExpression"!==r.type?Object.assign({},r,{type:"NGChainedExpression",expressions:[r]}):r}}},n)}var et={parsers:{__ng_action:Ze((function(e,t){return t.parseAction(e)})),__ng_binding:Ze((function(e,t){return t.parseBinding(e)})),__ng_interpolation:Ze((function(e,t){return t.parseInterpolation(e)})),__ng_directive:Ze((function(e,t){return t.parseTemplateBindings(e)}))}},tt=et.parsers;e.default=et,e.parsers=tt,Object.defineProperty(e,"__esModule",{value:!0})}));
