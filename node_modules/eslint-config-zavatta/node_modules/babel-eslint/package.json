{"name": "babel-es<PERSON>", "version": "7.2.3", "description": "Custom parser for ESLint", "main": "index.js", "files": ["index.js", "babylon-to-espree"], "repository": {"type": "git", "url": "https://github.com/babel/babel-eslint.git"}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-traverse": "^6.23.1", "babel-types": "^6.23.0", "babylon": "^6.17.0"}, "scripts": {"test": "npm run lint && npm run test-only", "test-only": "mocha", "lint": "eslint index.js babylon-to-espree test", "fix": "eslint index.js babylon-to-espree test --fix", "preversion": "npm test", "changelog": "git log `git describe --tags --abbrev=0`..HEAD --pretty=format:' * %s (%an)' | grep -v 'Merge pull request'"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=4"}, "bugs": {"url": "https://github.com/babel/babel-eslint/issues"}, "homepage": "https://github.com/babel/babel-eslint", "devDependencies": {"babel-eslint": "^7.0.0", "dedent": "^0.7.0", "eslint": "^3.18.0", "eslint-config-babel": "^6.0.0", "eslint-plugin-flowtype": "^2.30.3", "espree": "^3.4.0", "mocha": "^3.0.0"}}